import { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Table, Badge, Navbar, Nav, Tab, Tabs } from 'react-bootstrap';
import { toast } from 'react-toastify';
import axiosInstance from '../utils/axiosConfig';
import { RECIPE_ROUTES } from '../utils/apiRoutes';
import { getUserData, clearUserData, isAdmin, isChef } from '../utils/auth';
import RecipeModal from '../components/RecipeModal';
import FeedbackManagement from '../components/FeedbackManagement';

const DashboardHome = () => {
  const [recipes, setRecipes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [editingRecipe, setEditingRecipe] = useState(null);
  const [activeTab, setActiveTab] = useState('recipes');
  const userData = getUserData();

  useEffect(() => {
    fetchRecipes();
  }, []);

  const fetchRecipes = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(RECIPE_ROUTES.GET_ALL);
      if (response.data.success) {
        setRecipes(response.data.recipes);
      }
    } catch (error) {
      console.error('Error fetching recipes:', error);
      toast.error('Failed to fetch recipes');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRecipe = async (recipeId) => {
    if (!window.confirm('Are you sure you want to delete this recipe?')) {
      return;
    }

    try {
      const response = await axiosInstance.delete(RECIPE_ROUTES.DELETE(recipeId));
      if (response.data.success) {
        toast.success('Recipe deleted successfully');
        fetchRecipes();
      }
    } catch (error) {
      console.error('Error deleting recipe:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete recipe';
      toast.error(errorMessage);
    }
  };

  const handleEditRecipe = (recipe) => {
    setEditingRecipe(recipe);
    setShowModal(true);
  };

  const handleAddRecipe = () => {
    setEditingRecipe(null);
    setShowModal(true);
  };

  const handleModalClose = () => {
    setShowModal(false);
    setEditingRecipe(null);
    fetchRecipes(); // Refresh recipes after modal closes
  };

  const handleLogout = () => {
    clearUserData();
    toast.success('Logged out successfully');
    window.location.href = '/dashboard/login';
  };

  const canDeleteRecipe = (recipe) => {
    return isAdmin() || (isChef() && recipe.createdBy === userData.id);
  };

  const canEditRecipe = (recipe) => {
    return isAdmin() || (isChef() && recipe.createdBy === userData.id);
  };

  return (
    <>
      {/* Navigation Bar */}
      <Navbar bg="dark" variant="dark" expand="lg" className="mb-4">
        <Container>
          <Navbar.Brand>
            <i className="fas fa-utensils me-2"></i>
            Cookify Dashboard
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="ms-auto">
              <Nav.Link className="text-white">
                Welcome, {userData?.username}! 
                <Badge bg="primary" className="ms-2">{userData?.role}</Badge>
              </Nav.Link>
              <Button variant="outline-light" onClick={handleLogout}>
                Logout
              </Button>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>

      <Container>
        <Row className="mb-4">
          <Col>
            <h2>Dashboard Overview</h2>
            <p className="text-muted">
              Manage recipes and view feedback from users
            </p>
          </Col>
        </Row>

        {/* Dashboard Tabs */}
        <Tabs
          activeKey={activeTab}
          onSelect={(k) => setActiveTab(k)}
          className="mb-4"
        >
          <Tab eventKey="recipes" title={<><i className="fas fa-book me-2"></i>Recipes</>}>
            {/* Recipe Management Section */}
            <Row className="mb-4">
              <Col>
                <div className="d-flex justify-content-between align-items-center">
                  <h4>Recipe Management</h4>
                  <Button variant="primary" onClick={handleAddRecipe}>
                    <i className="fas fa-plus me-2"></i>
                    Add New Recipe
                  </Button>
                </div>
              </Col>
            </Row>

            {loading ? (
              <Row className="justify-content-center">
                <Col md={6} className="text-center">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  <p className="mt-2">Loading recipes...</p>
                </Col>
              </Row>
            ) : (
              <Card>
                <Card.Body>
                  {recipes.length > 0 ? (
                    <Table responsive striped hover>
                      <thead>
                        <tr>
                          <th>Image</th>
                          <th>Name</th>
                          <th>Description</th>
                          <th>Created By</th>
                          <th>Created At</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {recipes.map((recipe) => (
                          <tr key={recipe._id}>
                            <td>
                              <img
                                src={recipe.image}
                                alt={recipe.name}
                                style={{ width: '60px', height: '60px', objectFit: 'cover' }}
                                className="rounded"
                              />
                            </td>
                            <td>
                              <strong>{recipe.name}</strong>
                            </td>
                            <td>
                              {recipe.description.length > 100
                                ? `${recipe.description.substring(0, 100)}...`
                                : recipe.description}
                            </td>
                            <td>
                              <Badge bg="secondary">
                                {recipe.createdBy?.username || 'Unknown'}
                              </Badge>
                            </td>
                            <td>
                              {new Date(recipe.createdAt).toLocaleDateString()}
                            </td>
                            <td>
                              <div className="d-flex gap-2">
                                {canEditRecipe(recipe) && (
                                  <Button
                                    variant="outline-primary"
                                    size="sm"
                                    onClick={() => handleEditRecipe(recipe)}
                                  >
                                    <i className="fas fa-edit"></i>
                                  </Button>
                                )}
                                {canDeleteRecipe(recipe) && (
                                  <Button
                                    variant="outline-danger"
                                    size="sm"
                                    onClick={() => handleDeleteRecipe(recipe._id)}
                                  >
                                    <i className="fas fa-trash"></i>
                                  </Button>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  ) : (
                    <div className="text-center py-4">
                      <i className="fas fa-book fa-3x text-muted mb-3"></i>
                      <h5>No recipes found</h5>
                      <p className="text-muted">Start by adding your first recipe!</p>
                      <Button variant="primary" onClick={handleAddRecipe}>
                        <i className="fas fa-plus me-2"></i>
                        Add Recipe
                      </Button>
                    </div>
                  )}
                </Card.Body>
              </Card>
            )}
          </Tab>

          {isAdmin() && (
            <Tab eventKey="feedback" title={<><i className="fas fa-comments me-2"></i>Feedback</>}>
              <FeedbackManagement />
            </Tab>
          )}
        </Tabs>
      </Container>

      {/* Recipe Modal */}
      <RecipeModal
        show={showModal}
        onHide={handleModalClose}
        recipe={editingRecipe}
      />
    </>
  );
};

export default DashboardHome;

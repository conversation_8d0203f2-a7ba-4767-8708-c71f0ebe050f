import { useState, useEffect } from 'react';
import { Row, Col, Card, Button, Table, Badge } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import axiosInstance from '../utils/axiosConfig';
import { RECIPE_ROUTES, FEEDBACK_ROUTES } from '../utils/apiRoutes';
import { getUserData, isAdmin, isChef } from '../utils/auth';

const DashboardHome = () => {
  const [stats, setStats] = useState({
    totalRecipes: 0,
    totalFeedback: 0,
    recentRecipes: 0,
    averageRating: 0
  });
  const [recentRecipes, setRecentRecipes] = useState([]);
  const [recentFeedback, setRecentFeedback] = useState([]);
  const [loading, setLoading] = useState(true);
  const userData = getUserData();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Fetch recipes
      const recipesResponse = await axiosInstance.get(RECIPE_ROUTES.GET_ALL);
      let recipes = [];
      if (recipesResponse.data.success) {
        recipes = recipesResponse.data.recipes;
        setRecentRecipes(recipes.slice(0, 5)); // Show 5 most recent
      }

      // Fetch feedback
      let feedbacks = [];
      if (isAdmin()) {
        try {
          const feedbackResponse = await axiosInstance.get(FEEDBACK_ROUTES.GET_ALL);
          if (feedbackResponse.data.success) {
            feedbacks = feedbackResponse.data.feedbacks || [];
            setRecentFeedback(feedbacks.slice(0, 5)); // Show 5 most recent
          }
        } catch (error) {
          console.error('Error fetching feedback:', error);
        }
      }

      // Calculate stats
      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const recentCount = recipes.filter(recipe =>
        new Date(recipe.createdAt) > weekAgo
      ).length;

      const avgRating = feedbacks.length > 0
        ? (feedbacks.reduce((sum, fb) => sum + fb.rating, 0) / feedbacks.length).toFixed(1)
        : 0;

      setStats({
        totalRecipes: recipes.length,
        totalFeedback: feedbacks.length,
        recentRecipes: recentCount,
        averageRating: avgRating
      });

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to fetch dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  if (loading) {
    return (
      <Row className="justify-content-center">
        <Col md={6} className="text-center py-5">
          <div className="spinner-border text-primary mb-3" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <h4>Loading Dashboard...</h4>
          <p className="text-muted">Please wait while we fetch your data</p>
        </Col>
      </Row>
    );
  }

  return (
    <>
      {/* Welcome Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex align-items-center justify-content-between">
            <div>
              <h1 className="h3 mb-1">
                {getGreeting()}, {userData?.username}! 👋
              </h1>
              <p className="text-muted mb-0">
                Welcome to your {userData?.role} dashboard
              </p>
            </div>
            <div className="d-none d-md-block">
              <Button as={Link} to="/dashboard/recipes" variant="primary" size="lg">
                <i className="fas fa-plus me-2"></i>
                Manage Recipes
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {/* Stats Cards */}
      <Row className="mb-4">
        <Col lg={3} md={6}>
          <Card className="border-0 shadow-sm h-100 stats-card">
            <Card.Body className="text-center">
              <div className="text-primary mb-2">
                <i className="fas fa-utensils fa-2x"></i>
              </div>
              <h3 className="h4 mb-1">{stats.totalRecipes}</h3>
              <p className="text-muted mb-0">Total Recipes</p>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={3} md={6}>
          <Card className="border-0 shadow-sm h-100 stats-card">
            <Card.Body className="text-center">
              <div className="text-success mb-2">
                <i className="fas fa-clock fa-2x"></i>
              </div>
              <h3 className="h4 mb-1">{stats.recentRecipes}</h3>
              <p className="text-muted mb-0">New This Week</p>
            </Card.Body>
          </Card>
        </Col>
        {isAdmin() && (
          <>
            <Col lg={3} md={6}>
              <Card className="border-0 shadow-sm h-100 stats-card">
                <Card.Body className="text-center">
                  <div className="text-info mb-2">
                    <i className="fas fa-comments fa-2x"></i>
                  </div>
                  <h3 className="h4 mb-1">{stats.totalFeedback}</h3>
                  <p className="text-muted mb-0">Total Feedback</p>
                </Card.Body>
              </Card>
            </Col>
            <Col lg={3} md={6}>
              <Card className="border-0 shadow-sm h-100 stats-card">
                <Card.Body className="text-center">
                  <div className="text-warning mb-2">
                    <i className="fas fa-star fa-2x"></i>
                  </div>
                  <h3 className="h4 mb-1">{stats.averageRating}</h3>
                  <p className="text-muted mb-0">Average Rating</p>
                </Card.Body>
              </Card>
            </Col>
          </>
        )}
      </Row>

      {/* Recent Activity */}
      <Row>
        <Col lg={8}>
          {/* Recent Recipes */}
          <Card className="border-0 shadow-sm mb-4">
            <Card.Header className="bg-white border-bottom">
              <div className="d-flex align-items-center justify-content-between">
                <h5 className="mb-0">
                  <i className="fas fa-utensils me-2"></i>
                  Recent Recipes
                </h5>
                <Button as={Link} to="/dashboard/recipes" variant="outline-primary" size="sm">
                  View All <i className="fas fa-arrow-right ms-1"></i>
                </Button>
              </div>
            </Card.Header>
            <Card.Body>
              {recentRecipes.length > 0 ? (
                <Table responsive hover className="mb-0">
                  <thead>
                    <tr>
                      <th>Recipe</th>
                      <th>Created By</th>
                      <th>Date</th>
                      <th>Steps</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recentRecipes.map((recipe) => (
                      <tr key={recipe._id}>
                        <td>
                          <div className="d-flex align-items-center">
                            <img
                              src={recipe.image}
                              alt={recipe.name}
                              className="rounded me-3"
                              style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                            />
                            <div>
                              <div className="fw-semibold">{recipe.name}</div>
                              <small className="text-muted">
                                {recipe.description.length > 50
                                  ? `${recipe.description.substring(0, 50)}...`
                                  : recipe.description}
                              </small>
                            </div>
                          </div>
                        </td>
                        <td>
                          <Badge bg="secondary">
                            {recipe.createdBy?.username || 'Unknown'}
                          </Badge>
                        </td>
                        <td>
                          <small className="text-muted">
                            {new Date(recipe.createdAt).toLocaleDateString()}
                          </small>
                        </td>
                        <td>
                          <Badge bg="primary">{recipe.steps?.length || 0} steps</Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              ) : (
                <div className="text-center py-4">
                  <i className="fas fa-utensils fa-2x text-muted mb-2"></i>
                  <p className="text-muted mb-0">No recent recipes</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          {/* Quick Actions */}
          <Card className="border-0 shadow-sm mb-4">
            <Card.Header className="bg-white border-bottom">
              <h5 className="mb-0">
                <i className="fas fa-bolt me-2"></i>
                Quick Actions
              </h5>
            </Card.Header>
            <Card.Body>
              <div className="d-grid gap-2">
                <Button as={Link} to="/dashboard/recipes" variant="primary">
                  <i className="fas fa-plus me-2"></i>
                  Add New Recipe
                </Button>
                <Button as={Link} to="/dashboard/recipes" variant="outline-primary">
                  <i className="fas fa-list me-2"></i>
                  Manage Recipes
                </Button>
                {isAdmin() && (
                  <Button as={Link} to="/dashboard/feedback" variant="outline-info">
                    <i className="fas fa-comments me-2"></i>
                    View Feedback
                  </Button>
                )}
              </div>
            </Card.Body>
          </Card>

          {/* Recent Feedback (Admin Only) */}
          {isAdmin() && (
            <Card className="border-0 shadow-sm">
              <Card.Header className="bg-white border-bottom">
                <div className="d-flex align-items-center justify-content-between">
                  <h5 className="mb-0">
                    <i className="fas fa-comments me-2"></i>
                    Recent Feedback
                  </h5>
                  <Button as={Link} to="/dashboard/feedback" variant="outline-primary" size="sm">
                    View All <i className="fas fa-arrow-right ms-1"></i>
                  </Button>
                </div>
              </Card.Header>
              <Card.Body>
                {recentFeedback.length > 0 ? (
                  <div className="space-y-3">
                    {recentFeedback.map((feedback, index) => (
                      <div key={index} className="border-bottom pb-2 mb-2">
                        <div className="d-flex align-items-center justify-content-between mb-1">
                          <small className="fw-semibold">
                            {feedback.user?.username || 'Anonymous'}
                          </small>
                          <div>
                            {[...Array(5)].map((_, i) => (
                              <i
                                key={i}
                                className={`fas fa-star ${
                                  i < feedback.rating ? 'text-warning' : 'text-muted'
                                }`}
                                style={{ fontSize: '0.75rem' }}
                              />
                            ))}
                          </div>
                        </div>
                        <p className="small text-muted mb-0">
                          {feedback.description.length > 80
                            ? `${feedback.description.substring(0, 80)}...`
                            : feedback.description}
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-3">
                    <i className="fas fa-comments fa-2x text-muted mb-2"></i>
                    <p className="text-muted mb-0 small">No recent feedback</p>
                  </div>
                )}
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>

      {/* Mobile Quick Actions */}
      <Row className="d-md-none mt-4">
        <Col>
          <Button as={Link} to="/dashboard/recipes" variant="primary" size="lg" className="w-100">
            <i className="fas fa-plus me-2"></i>
            Manage Recipes
          </Button>
        </Col>
      </Row>
    </>
  );
};

export default DashboardHome;

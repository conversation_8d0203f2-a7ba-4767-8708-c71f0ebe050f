import { Routes, Route, Navigate } from 'react-router-dom'
import { ToastContainer } from 'react-toastify'
import { useEffect } from 'react'
import DashboardLogin from './pages/DashboardLogin'
import DashboardHome from './pages/DashboardHome'
import RecipeManagement from './pages/RecipeManagement'
import FeedbackManagement from './pages/FeedbackManagement'
import ProtectedRoute from './components/ProtectedRoute'
import DashboardLayout from './components/DashboardLayout'
import './App.css'

function App() {
  useEffect(() => {
    // Set theme for dashboard
    document.documentElement.setAttribute('data-bs-theme', 'light')
  }, [])

  return (
    <div className="App">
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<Navigate to="/dashboard/login" replace />} />
        <Route path="/dashboard/login" element={<DashboardLogin />} />

        {/* Protected Dashboard Routes */}
        <Route path="/dashboard/home" element={
          <ProtectedRoute>
            <DashboardLayout>
              <DashboardHome />
            </DashboardLayout>
          </ProtectedRoute>
        } />

        <Route path="/dashboard/recipes" element={
          <ProtectedRoute>
            <DashboardLayout>
              <RecipeManagement />
            </DashboardLayout>
          </ProtectedRoute>
        } />

        <Route path="/dashboard/feedback" element={
          <ProtectedRoute>
            <DashboardLayout>
              <FeedbackManagement />
            </DashboardLayout>
          </ProtectedRoute>
        } />
      </Routes>

      <ToastContainer
        position="top-right"
        autoClose={4000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        toastClassName="custom-toast"
      />
    </div>
  )
}

export default App

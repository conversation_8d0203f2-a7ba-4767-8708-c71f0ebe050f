import { useState } from 'react';
import { Navbar, Nav, Container, Offcanvas, Button, Dropdown, Badge } from 'react-bootstrap';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { getUserData, clearUserData } from '../utils/auth';
import { toast } from 'react-toastify';

const Layout = ({ children }) => {
  const [showSidebar, setShowSidebar] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const userData = getUserData();

  const handleLogout = () => {
    clearUserData();
    toast.success('Logged out successfully');
    navigate('/login');
  };

  const navItems = [
    { path: '/', label: 'Dashboard', icon: 'fas fa-home' },
    { path: '/recipes', label: 'All Recipes', icon: 'fas fa-book' },
    { path: '/profile', label: 'Profile', icon: 'fas fa-user' },
  ];

  const isActiveRoute = (path) => {
    return location.pathname === path;
  };

  return (
    <>
      {/* Top Navigation Bar */}
      <Navbar bg="white" expand="lg" className="shadow-sm border-bottom sticky-top">
        <Container fluid>
          {/* Mobile Menu Toggle */}
          <Button
            variant="outline-primary"
            className="d-lg-none me-2"
            onClick={() => setShowSidebar(true)}
          >
            <i className="fas fa-bars"></i>
          </Button>

          {/* Brand */}
          <Navbar.Brand as={Link} to="/" className="fw-bold text-primary">
            <i className="fas fa-utensils me-2"></i>
            Cookify
          </Navbar.Brand>

          {/* Desktop Navigation */}
          <Nav className="d-none d-lg-flex me-auto">
            {navItems.map((item) => (
              <Nav.Link
                key={item.path}
                as={Link}
                to={item.path}
                className={`mx-2 px-3 py-2 rounded-pill ${
                  isActiveRoute(item.path) 
                    ? 'bg-primary text-white' 
                    : 'text-dark hover-bg-light'
                }`}
              >
                <i className={`${item.icon} me-2`}></i>
                {item.label}
              </Nav.Link>
            ))}
          </Nav>

          {/* User Menu */}
          <Dropdown align="end">
            <Dropdown.Toggle
              variant="outline-primary"
              className="d-flex align-items-center border-0"
              id="user-dropdown"
            >
              <img
                src={userData?.image || '/default-avatar.png'}
                alt="Profile"
                className="rounded-circle me-2"
                style={{ width: '32px', height: '32px', objectFit: 'cover' }}
              />
              <span className="d-none d-md-inline">{userData?.username}</span>
              <Badge bg="secondary" className="ms-2 d-none d-md-inline">
                {userData?.role}
              </Badge>
            </Dropdown.Toggle>

            <Dropdown.Menu className="shadow border-0">
              <Dropdown.Header>
                <div className="d-flex align-items-center">
                  <img
                    src={userData?.image || '/default-avatar.png'}
                    alt="Profile"
                    className="rounded-circle me-2"
                    style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                  />
                  <div>
                    <div className="fw-semibold">{userData?.username}</div>
                    <small className="text-muted">{userData?.email}</small>
                  </div>
                </div>
              </Dropdown.Header>
              <Dropdown.Divider />
              <Dropdown.Item as={Link} to="/profile">
                <i className="fas fa-user me-2"></i>
                Profile Settings
              </Dropdown.Item>
              <Dropdown.Divider />
              <Dropdown.Item onClick={handleLogout} className="text-danger">
                <i className="fas fa-sign-out-alt me-2"></i>
                Logout
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </Container>
      </Navbar>

      {/* Mobile Sidebar */}
      <Offcanvas show={showSidebar} onHide={() => setShowSidebar(false)} placement="start">
        <Offcanvas.Header closeButton>
          <Offcanvas.Title className="text-primary fw-bold">
            <i className="fas fa-utensils me-2"></i>
            Cookify
          </Offcanvas.Title>
        </Offcanvas.Header>
        <Offcanvas.Body>
          <Nav className="flex-column">
            {navItems.map((item) => (
              <Nav.Link
                key={item.path}
                as={Link}
                to={item.path}
                className={`mb-2 p-3 rounded ${
                  isActiveRoute(item.path) 
                    ? 'bg-primary text-white' 
                    : 'text-dark hover-bg-light'
                }`}
                onClick={() => setShowSidebar(false)}
              >
                <i className={`${item.icon} me-3`}></i>
                {item.label}
              </Nav.Link>
            ))}
          </Nav>
          
          <hr />
          
          {/* User Info in Mobile */}
          <div className="d-flex align-items-center mb-3">
            <img
              src={userData?.image || '/default-avatar.png'}
              alt="Profile"
              className="rounded-circle me-3"
              style={{ width: '48px', height: '48px', objectFit: 'cover' }}
            />
            <div>
              <div className="fw-semibold">{userData?.username}</div>
              <Badge bg="secondary">{userData?.role}</Badge>
            </div>
          </div>
          
          <Button
            variant="outline-danger"
            className="w-100"
            onClick={handleLogout}
          >
            <i className="fas fa-sign-out-alt me-2"></i>
            Logout
          </Button>
        </Offcanvas.Body>
      </Offcanvas>

      {/* Main Content */}
      <main className="main-content">
        <Container fluid className="py-4">
          {children}
        </Container>
      </main>
    </>
  );
};

export default Layout;

/* Cookify App Styles */
.App {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
  color: white;
  border-radius: 15px;
  margin: 2rem 0;
}

/* Card Hover Effects */
.card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Voice Controls */
.voice-controls {
  background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
  border-radius: 10px;
  padding: 1rem;
  border: 2px dashed #0d6efd;
}

/* Timer Styles */
.timer-display {
  font-family: 'Courier New', monospace;
  font-weight: bold;
}

/* Step Navigation */
.current-step {
  border-left: 4px solid #0d6efd;
  background: linear-gradient(90deg, rgba(13,110,253,0.1) 0%, rgba(13,110,253,0.05) 100%);
}

/* Feedback Stars */
.cursor-pointer {
  cursor: pointer;
}

.cursor-pointer:hover {
  transform: scale(1.1);
  transition: transform 0.1s ease-in-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    margin: 1rem 0;
    padding: 2rem 1rem;
  }

  .voice-controls .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .step-navigation .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

/* Loading Animations */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #0d6efd;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0b5ed7;
}

/* Cookify App Styles */
.App {
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Main Content */
.main-content {
  min-height: calc(100vh - 76px);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Card Styles */
.card {
  border: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

/* Recipe Card Specific */
.recipe-card {
  overflow: hidden;
}

.recipe-card .card-img-top {
  transition: transform 0.3s ease;
}

.recipe-card:hover .card-img-top {
  transform: scale(1.05);
}

/* Navigation Styles */
.navbar {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95) !important;
}

.hover-bg-light:hover {
  background-color: rgba(13, 110, 253, 0.1);
  transition: background-color 0.2s ease;
}

/* Button Styles */
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(13, 110, 253, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0b5ed7 0%, #0a58ca 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 110, 253, 0.4);
}

/* Voice Controls */
.voice-controls {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid rgba(13, 110, 253, 0.2);
  backdrop-filter: blur(10px);
}

/* Timer Styles */
.timer-display {
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-weight: 600;
  letter-spacing: 0.1em;
}

/* Step Navigation */
.current-step {
  border-left: 4px solid #0d6efd;
  background: linear-gradient(90deg, rgba(13,110,253,0.08) 0%, rgba(13,110,253,0.02) 100%);
  border-radius: 8px;
}

/* Form Styles */
.form-control {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: all 0.2s ease;
}

.form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
}

/* Toast Styles */
.custom-toast {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
}

/* Feedback Stars */
.cursor-pointer {
  cursor: pointer;
  transition: all 0.2s ease;
}

.cursor-pointer:hover {
  transform: scale(1.2);
  color: #ffc107 !important;
}

/* Loading States */
.spinner-border {
  width: 2rem;
  height: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem 0;
  }

  .voice-controls .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .step-navigation .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .card {
    margin-bottom: 1rem;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #0d6efd, #6610f2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #0b5ed7, #5a0fc8);
}

/* Animation Classes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Cooking Mode Styles */
.cooking-mode-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
  padding: 20px;
}

.cooking-mode-card {
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 15px;
}

.current-step-card {
  border-left: 4px solid #0d6efd;
  background: linear-gradient(90deg, rgba(13,110,253,0.1) 0%, rgba(13,110,253,0.05) 100%);
}

/* Voice Controls Enhanced */
.voice-controls {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid rgba(13, 110, 253, 0.2);
  backdrop-filter: blur(10px);
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(135deg, #0d6efd, #6610f2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

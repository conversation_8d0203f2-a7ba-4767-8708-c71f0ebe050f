{"name": "yargs", "version": "4.8.1", "description": "yargs the modern, pirate-themed, successor to optimist.", "main": "./index.js", "files": ["index.js", "yargs.js", "lib", "locales", "completion.sh.hbs", "LICENSE"], "dependencies": {"cliui": "^3.2.0", "decamelize": "^1.1.1", "get-caller-file": "^1.0.1", "lodash.assign": "^4.0.3", "os-locale": "^1.4.0", "read-pkg-up": "^1.0.1", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^1.0.1", "which-module": "^1.0.0", "window-size": "^0.2.0", "y18n": "^3.2.1", "yargs-parser": "^2.4.1"}, "devDependencies": {"chai": "^3.4.1", "chalk": "^1.1.3", "coveralls": "^2.11.11", "cpr": "^1.0.0", "cross-spawn": "^4.0.0", "es6-promise": "^3.0.2", "hashish": "0.0.4", "mocha": "^2.5.2", "nyc": "^7.0.0", "rimraf": "^2.5.0", "standard": "^7.0.0", "standard-version": "^2.2.1", "which": "^1.2.9"}, "scripts": {"pretest": "standard", "test": "nyc --cache mocha --require ./test/before.js --timeout=8000 --check-leaks", "coverage": "nyc report --reporter=text-lcov | coveralls", "version": "standard-version"}, "repository": {"type": "git", "url": "http://github.com/yargs/yargs.git"}, "homepage": "http://yargs.js.org/", "standard": {"ignore": ["**/example/**"]}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "license": "MIT", "engine": {"node": ">=0.10"}}
import { useState, useEffect } from 'react';
import { Card, Table, Badge, Button, Modal } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { getUserData } from '../utils/auth';

const SavedRecipes = () => {
  const [savedSessions, setSavedSessions] = useState([]);
  const [selectedSession, setSelectedSession] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const userData = getUserData();

  useEffect(() => {
    loadSavedSessions();
  }, []);

  const loadSavedSessions = () => {
    const sessions = JSON.parse(localStorage.getItem('cookingSessions') || '[]');
    // Filter sessions for current user
    const userSessions = sessions.filter(session => session.userId === userData?.id);
    setSavedSessions(userSessions.reverse()); // Show newest first
  };

  const deleteSavedSession = (sessionIndex) => {
    const sessions = JSON.parse(localStorage.getItem('cookingSessions') || '[]');
    const userSessions = sessions.filter(session => session.userId === userData?.id);
    const allSessions = sessions.filter(session => session.userId !== userData?.id);
    
    userSessions.splice(sessionIndex, 1);
    const updatedSessions = [...allSessions, ...userSessions];
    
    localStorage.setItem('cookingSessions', JSON.stringify(updatedSessions));
    loadSavedSessions();
  };

  const viewSessionDetails = (session) => {
    setSelectedSession(session);
    setShowModal(true);
  };

  const getCompletionPercentage = (completed, total) => {
    return Math.round((completed / total) * 100);
  };

  const getCompletionBadge = (completed, total) => {
    const percentage = getCompletionPercentage(completed, total);
    if (percentage === 100) return 'success';
    if (percentage >= 75) return 'info';
    if (percentage >= 50) return 'warning';
    return 'secondary';
  };

  return (
    <>
      <Card className="border-0 shadow-sm">
        <Card.Header className="bg-white border-bottom">
          <h5 className="mb-0">
            <i className="fas fa-bookmark me-2"></i>
            Saved Cooking Sessions ({savedSessions.length})
          </h5>
        </Card.Header>
        <Card.Body>
          {savedSessions.length > 0 ? (
            <Table responsive hover className="mb-0">
              <thead>
                <tr>
                  <th>Recipe</th>
                  <th>Progress</th>
                  <th>Date</th>
                  <th>Notes</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {savedSessions.map((session, index) => (
                  <tr key={index}>
                    <td>
                      <div className="fw-semibold">{session.recipeName}</div>
                    </td>
                    <td>
                      <div className="d-flex align-items-center">
                        <Badge 
                          bg={getCompletionBadge(session.completedSteps, session.totalSteps)}
                          className="me-2"
                        >
                          {getCompletionPercentage(session.completedSteps, session.totalSteps)}%
                        </Badge>
                        <small className="text-muted">
                          {session.completedSteps}/{session.totalSteps} steps
                        </small>
                      </div>
                    </td>
                    <td>
                      <small className="text-muted">
                        {new Date(session.date).toLocaleDateString()}
                      </small>
                    </td>
                    <td>
                      <small className="text-muted">
                        {session.notes ? (
                          session.notes.length > 30 
                            ? `${session.notes.substring(0, 30)}...`
                            : session.notes
                        ) : (
                          'No notes'
                        )}
                      </small>
                    </td>
                    <td>
                      <div className="d-flex gap-2">
                        <Button
                          variant="outline-info"
                          size="sm"
                          onClick={() => viewSessionDetails(session)}
                          title="View Details"
                        >
                          <i className="fas fa-eye"></i>
                        </Button>
                        <Button
                          as={Link}
                          to={`/recipe/${session.recipeId}`}
                          variant="outline-primary"
                          size="sm"
                          title="Cook Again"
                        >
                          <i className="fas fa-redo"></i>
                        </Button>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => deleteSavedSession(index)}
                          title="Delete Session"
                        >
                          <i className="fas fa-trash"></i>
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          ) : (
            <div className="text-center py-5">
              <i className="fas fa-bookmark fa-3x text-muted mb-3"></i>
              <h5>No saved cooking sessions</h5>
              <p className="text-muted">
                Start cooking with voice mode and save your sessions to see them here!
              </p>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Session Details Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title>Cooking Session Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedSession && (
            <div>
              <div className="mb-3">
                <h6>Recipe: {selectedSession.recipeName}</h6>
                <p className="text-muted mb-1">
                  Cooked on: {new Date(selectedSession.date).toLocaleDateString()} at{' '}
                  {new Date(selectedSession.date).toLocaleTimeString()}
                </p>
              </div>

              <div className="mb-3">
                <h6>Progress</h6>
                <div className="d-flex align-items-center">
                  <div className="progress flex-grow-1 me-3" style={{ height: '20px' }}>
                    <div
                      className={`progress-bar bg-${getCompletionBadge(selectedSession.completedSteps, selectedSession.totalSteps)}`}
                      style={{
                        width: `${getCompletionPercentage(selectedSession.completedSteps, selectedSession.totalSteps)}%`
                      }}
                    >
                      {getCompletionPercentage(selectedSession.completedSteps, selectedSession.totalSteps)}%
                    </div>
                  </div>
                  <span className="text-muted">
                    {selectedSession.completedSteps}/{selectedSession.totalSteps} steps
                  </span>
                </div>
              </div>

              {selectedSession.notes && (
                <div className="mb-3">
                  <h6>Your Notes</h6>
                  <div className="bg-light p-3 rounded">
                    <p className="mb-0">{selectedSession.notes}</p>
                  </div>
                </div>
              )}

              <div className="d-flex gap-2">
                <Button
                  as={Link}
                  to={`/recipe/${selectedSession.recipeId}`}
                  variant="primary"
                  onClick={() => setShowModal(false)}
                >
                  <i className="fas fa-redo me-2"></i>
                  Cook This Recipe Again
                </Button>
              </div>
            </div>
          )}
        </Modal.Body>
      </Modal>
    </>
  );
};

export default SavedRecipes;

import { Navigate } from 'react-router-dom';
import { isAuthenticated, isChef, isAdmin } from '../utils/auth';

const ChefRoute = ({ children }) => {
  const isLoggedIn = isAuthenticated();
  const canAccess = isChef() || isAdmin();
  
  if (!isLoggedIn) {
    return <Navigate to="/login" replace />;
  }
  
  if (!canAccess) {
    return <Navigate to="/" replace />;
  }
  
  return children;
};

export default ChefRoute;

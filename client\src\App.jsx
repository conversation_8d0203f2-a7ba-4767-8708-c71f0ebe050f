import { Routes, Route } from 'react-router-dom'
import { ToastContainer } from 'react-toastify'
import { useEffect } from 'react'
import Layout from './components/Layout'
import Home from './pages/Home'
import Login from './pages/Login'
import Signup from './pages/Signup'
import RecipeDetail from './pages/RecipeDetail'
import Recipes from './pages/Recipes'
import MyRecipes from './pages/MyRecipes'
import Profile from './pages/Profile'
import ProtectedRoute from './components/ProtectedRoute'
import ChefRoute from './components/ChefRoute'
import './App.css'

function App() {
  useEffect(() => {
    // Set theme
    document.documentElement.setAttribute('data-bs-theme', 'light')
  }, [])

  return (
    <div className="App">
      <Routes>
        {/* Public Routes */}
        <Route path="/login" element={<Login />} />
        <Route path="/signup" element={<Signup />} />

        {/* Protected Routes with Layout */}
        <Route path="/" element={
          <ProtectedRoute>
            <Layout>
              <Home />
            </Layout>
          </ProtectedRoute>
        } />

        <Route path="/recipes" element={
          <ProtectedRoute>
            <Layout>
              <Recipes />
            </Layout>
          </ProtectedRoute>
        } />

        <Route path="/my-recipes" element={
          <ChefRoute>
            <Layout>
              <MyRecipes />
            </Layout>
          </ChefRoute>
        } />

        <Route path="/recipe/:id" element={
          <ProtectedRoute>
            <Layout>
              <RecipeDetail />
            </Layout>
          </ProtectedRoute>
        } />

        <Route path="/profile" element={
          <ProtectedRoute>
            <Layout>
              <Profile />
            </Layout>
          </ProtectedRoute>
        } />
      </Routes>

      <ToastContainer
        position="top-right"
        autoClose={4000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        toastClassName="custom-toast"
      />
    </div>
  )
}

export default App

/* Cookify Dashboard Styles */
.App {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* Dashboard Cards */
.card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border: none;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

/* Table Styles */
.table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
}

/* Modal Styles */
.modal-content {
  border: none;
  box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
  background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
  color: white;
  border-bottom: none;
}

.modal-header .btn-close {
  filter: invert(1);
}

/* Form Styles */
.form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Button Styles */
.btn-primary {
  background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0b5ed7 0%, #5a0fc8 100%);
  transform: translateY(-1px);
}

/* Statistics Cards */
.stats-card {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border-left: 4px solid #0d6efd;
}

/* Progress Bars */
.progress {
  background-color: #e9ecef;
}

.progress-bar {
  background: linear-gradient(90deg, #0d6efd 0%, #6610f2 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }

  .btn-group .btn {
    padding: 0.25rem 0.5rem;
  }

  .modal-dialog {
    margin: 1rem;
  }
}

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255,255,255,0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #0d6efd;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0b5ed7;
}

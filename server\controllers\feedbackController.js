const Feedback = require("../models/Feedback");
const User = require("../models/User");
const Recipe = require("../models/Recipe");
require("dotenv").config();

exports.createFeedback = async (req, res) => {
  try {
    const { description, rating } = req.body;
    const userId = req.user.id;
    const recipeId = req.params.id;

    if (!description || rating === undefined) {
      return res.status(400).json({
        success: false,
        message: "Description and rating are required",
      });
    }

    const recipe = await Recipe.findById(recipeId);
    if (!recipe) {
      return res.status(404).json({
        success: false,
        message: "Recipe not found",
      });
    }

    const feedback = await Feedback.create({
      user: userId,
      recipe: recipeId,
      description,
      rating,
    });

    return res.status(201).json({
      success: true,
      message: "Feedback submitted successfully",
      feedback,
    });
  } catch (error) {
    console.error("Create Feedback Error:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to submit feedback",
    });
  }
};


exports.getAllFeedback = async (req, res) => {
  try {
    const recipeId = req.query.recipeId;

    let feedbacks = await Feedback.find().populate("user", "username email");
    return res.status(200).json({
      success: true,
      count: feedbacks.length,
      feedbacks,
    });
  } catch (error) {
    console.error("Get All Feedback Error:", error);
    return res.status(500).json({
      success: false,
      message: "Error fetching feedback",
    });
  }
};

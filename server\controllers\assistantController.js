const Recipe = require("../models/Recipe");
const gtts = require("gtts");
const fs = require("fs");
const path = require("path");

exports.getStepVoice = async (req, res) => {
  try {
    const { recipeId, stepIndex, action } = req.body;

    if (stepIndex === undefined || !recipeId || !action) {
      return res.status(400).json({
        success: false,
        message: "recipeId, stepIndex and action are required",
      });
    }

    const recipe = await Recipe.findById(recipeId);
    if (!recipe || !recipe.steps || stepIndex >= recipe.steps.length) {
      return res.status(404).json({
        success: false,
        message: "Step not found",
      });
    }

    const step = recipe.steps[stepIndex];
    let sentence = "";

    if (action === "start" || action === "next") {
      sentence = `Step ${stepIndex + 1}: ${step}`;
    } else if (action === "repeat") {
      sentence = `Repeating step ${stepIndex + 1}: ${step}`;
    } else {
      return res.status(400).json({ success: false, message: "Invalid action" });
    }

    try {
      // Create GTTS instance with English language
      const tts = new gtts(sentence, 'en');

      // Create audio directory if it doesn't exist
      const audioDir = path.join(__dirname, '../public/audio');
      if (!fs.existsSync(audioDir)) {
        fs.mkdirSync(audioDir, { recursive: true });
      }

      // Generate unique filename
      const filename = `step_${recipeId}_${stepIndex}_${Date.now()}.mp3`;
      const filepath = path.join(audioDir, filename);

      // Save audio file
      await new Promise((resolve, reject) => {
        tts.save(filepath, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      // Return URL to audio file
      const audioUrl = `/audio/${filename}`;

      return res.status(200).json({
        success: true,
        message: "Voice generated successfully",
        audioUrl,
        currentStep: stepIndex,
        text: sentence
      });

    } catch (ttsError) {
      console.error("TTS Error:", ttsError);

      // Fallback: return text for client-side speech synthesis
      return res.status(200).json({
        success: true,
        message: "Voice generation fallback",
        audioUrl: null,
        currentStep: stepIndex,
        text: sentence,
        fallback: true
      });
    }

  } catch (error) {
    console.error("Step voice error:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to generate voice",
      error: error.message
    });
  }
};

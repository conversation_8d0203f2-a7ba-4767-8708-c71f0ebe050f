const Recipe = require("../models/Recipe");
const gtts = require("google-translate-tts");

exports.getStepVoice = async (req, res) => {
  try {
    const { recipeId, stepIndex, action } = req.body;

    if (stepIndex === undefined || !recipeId || !action) {
      return res.status(400).json({
        success: false,
        message: "recipeId, stepIndex and action are required",
      });
    }

    const recipe = await Recipe.findById(recipeId);
    if (!recipe || !recipe.steps || stepIndex >= recipe.steps.length) {
      return res.status(404).json({
        success: false,
        message: "Step not found",
      });
    }

    const step = recipe.steps[stepIndex];
    let sentence = "";

    if (action === "start" || action === "next") {
      sentence = `Step ${stepIndex + 1}: ${step}.`;
    } else if (action === "repeat") {
      sentence = `Step ${stepIndex + 1}: ${step}.`;
    } else {
      return res.status(400).json({ success: false, message: "Invalid action" });
    }

    const audioUrl = await gtts.getAudioUrl(sentence, {
      lang: "hi",
      slow: false,
      host: "https://translate.google.com",
    });

    return res.status(200).json({
      success: true,
      message: "Voice generated",
      audioUrl,
      currentStep: stepIndex,
    });
  } catch (error) {
    console.error("Step voice error:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to generate voice",
    });
  }
};

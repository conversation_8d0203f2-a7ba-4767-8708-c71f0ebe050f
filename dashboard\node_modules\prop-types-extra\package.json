{"name": "prop-types-extra", "version": "1.1.1", "description": "React PropType Utilities", "main": "lib/index.js", "files": ["LICENSE", "README.md", "lib"], "scripts": {"build": "rm -rf lib && babel src --out-dir lib", "lint": "eslint .", "test": "npm run lint && jest", "tdd": "jest --watch", "release": "release"}, "repository": {"type": "git", "url": "git+https://github.com/react-bootstrap/prop-types-extra.git"}, "keywords": ["react", "proptypes"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/react-bootstrap/prop-types-extra/issues"}, "homepage": "https://github.com/react-bootstrap/prop-types-extra#readme", "jest": {"roots": ["<rootDir>/test"], "testRegex": "test\\.js", "setupTestFrameworkScriptFile": "./test/test-setup.js"}, "peerDependencies": {"react": ">=0.14.0"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-eslint": "^7.2.3", "babel-jest": "^20.0.1", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "^1.4.0", "babel-preset-react": "^6.24.1", "chai": "^3.0.0", "dirty-chai": "^1.2.2", "eslint": "^2.13.1", "eslint-config-airbnb": "^9.0.1", "eslint-config-airbnb-base": "^3.0.1", "eslint-plugin-import": "^1.10.2", "eslint-plugin-jsx-a11y": "^1.5.5", "eslint-plugin-mocha": "^4.0.0", "eslint-plugin-react": "^5.2.2", "jest": "^20.0.1", "prop-types": "^15.6.1", "react": "^16.3.2", "release-script": "^1.0.2", "sinon": "^2.2.0", "sinon-chai": "^2.10.0"}, "dependencies": {"react-is": "^16.3.2", "warning": "^4.0.0"}}
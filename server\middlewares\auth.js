const jwt = require("jsonwebtoken");
require("dotenv").config();

exports.isAuth = (req, res, next) => {
  try {
    const token = req.cookies.token;
    if (!token) {
      return res.status(401).json({
        success: false,
        message: "To<PERSON> is missing. Please log in.",
      });
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      req.user = decoded;
      next();
    } catch (err) {
      return res.status(401).json({
        success: false,
        message: "Invalid token. Please log in again.",
      });
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Something went wrong with authentication",
    });
  }
};

exports.isUser = (req, res, next) => {
  try {
    if (req.user.role !== "User") {
      return res.status(403).json({
        success: false,
        message: "Access denied. User only route.",
      });
    }
    next();
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Error verifying user role",
    });
  }
};

exports.isChef = (req, res, next) => {
  try {
    if (req.user.role !== "Chef") {
      return res.status(403).json({
        success: false,
        message: "Access denied. Chef only route.",
      });
    }
    next();
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Error verifying Chef role",
    });
  }
};
import { Navigate } from 'react-router-dom';
import { isAuthenticated, getUserRole } from '../utils/auth';

const ProtectedRoute = ({ children }) => {
  const isLoggedIn = isAuthenticated();
  const userRole = getUserRole();
  
  if (!isLoggedIn) {
    return <Navigate to="/dashboard/login" replace />;
  }
  
  // Only allow Chef and Admin roles in dashboard
  if (userRole !== 'Chef' && userRole !== 'Admin') {
    return <Navigate to="/dashboard/login" replace />;
  }
  
  return children;
};

export default ProtectedRoute;

{"version": 3, "file": "popper.js", "sources": ["../../src/dom-utils/getWindow.js", "../../src/dom-utils/instanceOf.js", "../../src/utils/math.js", "../../src/utils/userAgent.js", "../../src/dom-utils/isLayoutViewport.js", "../../src/dom-utils/getBoundingClientRect.js", "../../src/dom-utils/getWindowScroll.js", "../../src/dom-utils/getHTMLElementScroll.js", "../../src/dom-utils/getNodeScroll.js", "../../src/dom-utils/getNodeName.js", "../../src/dom-utils/getDocumentElement.js", "../../src/dom-utils/getWindowScrollBarX.js", "../../src/dom-utils/getComputedStyle.js", "../../src/dom-utils/isScrollParent.js", "../../src/dom-utils/getCompositeRect.js", "../../src/dom-utils/getLayoutRect.js", "../../src/dom-utils/getParentNode.js", "../../src/dom-utils/getScrollParent.js", "../../src/dom-utils/listScrollParents.js", "../../src/dom-utils/isTableElement.js", "../../src/dom-utils/getOffsetParent.js", "../../src/enums.js", "../../src/utils/orderModifiers.js", "../../src/utils/debounce.js", "../../src/utils/mergeByName.js", "../../src/dom-utils/getViewportRect.js", "../../src/dom-utils/getDocumentRect.js", "../../src/dom-utils/contains.js", "../../src/utils/rectToClientRect.js", "../../src/dom-utils/getClippingRect.js", "../../src/utils/getBasePlacement.js", "../../src/utils/getVariation.js", "../../src/utils/getMainAxisFromPlacement.js", "../../src/utils/computeOffsets.js", "../../src/utils/getFreshSideObject.js", "../../src/utils/mergePaddingObject.js", "../../src/utils/expandToHashMap.js", "../../src/utils/detectOverflow.js", "../../src/createPopper.js", "../../src/modifiers/eventListeners.js", "../../src/modifiers/popperOffsets.js", "../../src/modifiers/computeStyles.js", "../../src/modifiers/applyStyles.js", "../../src/modifiers/offset.js", "../../src/utils/getOppositePlacement.js", "../../src/utils/getOppositeVariationPlacement.js", "../../src/utils/computeAutoPlacement.js", "../../src/modifiers/flip.js", "../../src/utils/getAltAxis.js", "../../src/utils/within.js", "../../src/modifiers/preventOverflow.js", "../../src/modifiers/arrow.js", "../../src/modifiers/hide.js", "../../src/popper-lite.js", "../../src/popper.js"], "sourcesContent": ["// @flow\nimport type { Window } from '../types';\ndeclare function getWindow(node: Node | Window): Window;\n\nexport default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    const ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}\n", "// @flow\nimport getWindow from './getWindow';\n\ndeclare function isElement(node: mixed): boolean %checks(node instanceof\n  Element);\nfunction isElement(node) {\n  const OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\ndeclare function isHTMLElement(node: mixed): boolean %checks(node instanceof\n  HTMLElement);\nfunction isHTMLElement(node) {\n  const OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\ndeclare function isShadowRoot(node: mixed): boolean %checks(node instanceof\n  ShadowRoot);\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  const OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };\n", "// @flow\nexport const max = Math.max;\nexport const min = Math.min;\nexport const round = Math.round;\n", "// @flow\ntype Navigator = Navigator & { userAgentData?: NavigatorUAData };\n\ninterface NavigatorUAData {\n  brands: Array<{ brand: string, version: string }>;\n  mobile: boolean;\n  platform: string;\n}\n\nexport default function getUAString(): string {\n  const uaData = (navigator: Navigator).userAgentData;\n\n  if (uaData?.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands\n      .map((item) => `${item.brand}/${item.version}`)\n      .join(' ');\n  }\n\n  return navigator.userAgent;\n}\n", "// @flow\nimport getUAString from '../utils/userAgent';\n\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}\n", "// @flow\nimport type { ClientRectObject, VirtualElement } from '../types';\nimport { isElement, isHTMLElement } from './instanceOf';\nimport { round } from '../utils/math';\nimport getWindow from './getWindow';\nimport isLayoutViewport from './isLayoutViewport';\n\nexport default function getBoundingClientRect(\n  element: Element | VirtualElement,\n  includeScale: boolean = false,\n  isFixedStrategy: boolean = false\n): ClientRectObject {\n  const clientRect = element.getBoundingClientRect();\n  let scaleX = 1;\n  let scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX =\n      (element: HTMLElement).offsetWidth > 0\n        ? round(clientRect.width) / (element: HTMLElement).offsetWidth || 1\n        : 1;\n    scaleY =\n      (element: HTMLElement).offsetHeight > 0\n        ? round(clientRect.height) / (element: HTMLElement).offsetHeight || 1\n        : 1;\n  }\n\n  const { visualViewport } = isElement(element) ? getWindow(element) : window;\n  const addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n\n  const x =\n    (clientRect.left +\n      (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) /\n    scaleX;\n  const y =\n    (clientRect.top +\n      (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) /\n    scaleY;\n  const width = clientRect.width / scaleX;\n  const height = clientRect.height / scaleY;\n\n  return {\n    width,\n    height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x,\n    y,\n  };\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport type { Window } from '../types';\n\nexport default function getWindowScroll(node: Node | Window) {\n  const win = getWindow(node);\n  const scrollLeft = win.pageXOffset;\n  const scrollTop = win.pageYOffset;\n\n  return {\n    scrollLeft,\n    scrollTop,\n  };\n}\n", "// @flow\n\nexport default function getHTMLElementScroll(element: HTMLElement) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop,\n  };\n}\n", "// @flow\nimport getWindowScroll from './getWindowScroll';\nimport getWindow from './getWindow';\nimport { isHTMLElement } from './instanceOf';\nimport getHTMLElementScroll from './getHTMLElementScroll';\nimport type { Window } from '../types';\n\nexport default function getNodeScroll(node: Node | Window) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}\n", "// @flow\nimport type { Window } from '../types';\n\nexport default function getNodeName(element: ?Node | Window): ?string {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}\n", "// @flow\nimport { isElement } from './instanceOf';\nimport type { Window } from '../types';\n\nexport default function getDocumentElement(\n  element: Element | Window\n): HTMLElement {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return (\n    (isElement(element)\n      ? element.ownerDocument\n      : // $FlowFixMe[prop-missing]\n        element.document) || window.document\n  ).documentElement;\n}\n", "// @flow\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScroll from './getWindowScroll';\n\nexport default function getWindowScrollBarX(element: Element): number {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return (\n    getBoundingClientRect(getDocumentElement(element)).left +\n    getWindowScroll(element).scrollLeft\n  );\n}\n", "// @flow\nimport getWindow from './getWindow';\n\nexport default function getComputedStyle(\n  element: Element\n): CSSStyleDeclaration {\n  return getWindow(element).getComputedStyle(element);\n}\n", "// @flow\nimport getComputedStyle from './getComputedStyle';\n\nexport default function isScrollParent(element: HTMLElement): boolean {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}\n", "// @flow\nimport type { Rect, VirtualElement, Window } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getNodeScroll from './getNodeScroll';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getDocumentElement from './getDocumentElement';\nimport isScrollParent from './isScrollParent';\nimport { round } from '../utils/math';\n\nfunction isElementScaled(element: HTMLElement) {\n  const rect = element.getBoundingClientRect();\n  const scaleX = round(rect.width) / element.offsetWidth || 1;\n  const scaleY = round(rect.height) / element.offsetHeight || 1;\n\n  return scaleX !== 1 || scaleY !== 1;\n}\n\n// Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\nexport default function getCompositeRect(\n  elementOrVirtualElement: Element | VirtualElement,\n  offsetParent: Element | Window,\n  isFixed: boolean = false\n): Rect {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const offsetParentIsScaled =\n    isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const rect = getBoundingClientRect(\n    elementOrVirtualElement,\n    offsetParentIsScaled,\n    isFixed\n  );\n\n  let scroll = { scrollLeft: 0, scrollTop: 0 };\n  let offsets = { x: 0, y: 0 };\n\n  if (isOffsetParentAnElement || (!isOffsetParentAnElement && !isFixed)) {\n    if (\n      getNodeName(offsetParent) !== 'body' ||\n      // https://github.com/popperjs/popper-core/issues/1078\n      isScrollParent(documentElement)\n    ) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height,\n  };\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\n\n// Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\nexport default function getLayoutRect(element: HTMLElement): Rect {\n  const clientRect = getBoundingClientRect(element);\n\n  // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n  let width = element.offsetWidth;\n  let height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width,\n    height,\n  };\n}\n", "// @flow\nimport getNodeName from './getNodeName';\nimport getDocumentElement from './getDocumentElement';\nimport { isShadowRoot } from './instanceOf';\n\nexport default function getParentNode(element: Node | ShadowRoot): Node {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (\n    // this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || // DOM Element detected\n    (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n  );\n}\n", "// @flow\nimport getParentNode from './getParentNode';\nimport isScrollParent from './isScrollParent';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\n\nexport default function getScrollParent(node: Node): HTMLElement {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}\n", "// @flow\nimport getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getWindow from './getWindow';\nimport type { Window, VisualViewport } from '../types';\nimport isScrollParent from './isScrollParent';\n\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\nexport default function listScrollParents(\n  element: Node,\n  list: Array<Element | Window> = []\n): Array<Element | Window | VisualViewport> {\n  const scrollParent = getScrollParent(element);\n  const isBody = scrollParent === element.ownerDocument?.body;\n  const win = getWindow(scrollParent);\n  const target = isBody\n    ? [win].concat(\n        win.visualViewport || [],\n        isScrollParent(scrollParent) ? scrollParent : []\n      )\n    : scrollParent;\n  const updatedList = list.concat(target);\n\n  return isBody\n    ? updatedList\n    : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n      updatedList.concat(listScrollParents(getParentNode(target)));\n}\n", "// @flow\nimport getNodeName from './getNodeName';\n\nexport default function isTableElement(element: Element): boolean {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport getNodeName from './getNodeName';\nimport getComputedStyle from './getComputedStyle';\nimport { isHTMLElement, isShadowRoot } from './instanceOf';\nimport isTableElement from './isTableElement';\nimport getParentNode from './getParentNode';\nimport getUAString from '../utils/userAgent';\n\nfunction getTrueOffsetParent(element: Element): ?Element {\n  if (\n    !isHTMLElement(element) ||\n    // https://github.com/popperjs/popper-core/issues/837\n    getComputedStyle(element).position === 'fixed'\n  ) {\n    return null;\n  }\n\n  return element.offsetParent;\n}\n\n// `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\nfunction getContainingBlock(element: Element) {\n  const isFirefox = /firefox/i.test(getUAString());\n  const isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    const elementCss = getComputedStyle(element);\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  let currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (\n    isHTMLElement(currentNode) &&\n    ['html', 'body'].indexOf(getNodeName(currentNode)) < 0\n  ) {\n    const css = getComputedStyle(currentNode);\n\n    // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n    if (\n      css.transform !== 'none' ||\n      css.perspective !== 'none' ||\n      css.contain === 'paint' ||\n      ['transform', 'perspective'].indexOf(css.willChange) !== -1 ||\n      (isFirefox && css.willChange === 'filter') ||\n      (isFirefox && css.filter && css.filter !== 'none')\n    ) {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nexport default function getOffsetParent(element: Element) {\n  const window = getWindow(element);\n\n  let offsetParent = getTrueOffsetParent(element);\n\n  while (\n    offsetParent &&\n    isTableElement(offsetParent) &&\n    getComputedStyle(offsetParent).position === 'static'\n  ) {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (\n    offsetParent &&\n    (getNodeName(offsetParent) === 'html' ||\n      (getNodeName(offsetParent) === 'body' &&\n        getComputedStyle(offsetParent).position === 'static'))\n  ) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}\n", "// @flow\nexport const top: 'top' = 'top';\nexport const bottom: 'bottom' = 'bottom';\nexport const right: 'right' = 'right';\nexport const left: 'left' = 'left';\nexport const auto: 'auto' = 'auto';\nexport type BasePlacement =\n  | typeof top\n  | typeof bottom\n  | typeof right\n  | typeof left;\nexport const basePlacements: Array<BasePlacement> = [top, bottom, right, left];\n\nexport const start: 'start' = 'start';\nexport const end: 'end' = 'end';\nexport type Variation = typeof start | typeof end;\n\nexport const clippingParents: 'clippingParents' = 'clippingParents';\nexport const viewport: 'viewport' = 'viewport';\nexport type Boundary = Element | Array<Element> | typeof clippingParents;\nexport type RootBoundary = typeof viewport | 'document';\n\nexport const popper: 'popper' = 'popper';\nexport const reference: 'reference' = 'reference';\nexport type Context = typeof popper | typeof reference;\n\nexport type VariationPlacement =\n  | 'top-start'\n  | 'top-end'\n  | 'bottom-start'\n  | 'bottom-end'\n  | 'right-start'\n  | 'right-end'\n  | 'left-start'\n  | 'left-end';\nexport type AutoPlacement = 'auto' | 'auto-start' | 'auto-end';\nexport type ComputedPlacement = VariationPlacement | BasePlacement;\nexport type Placement = AutoPlacement | BasePlacement | VariationPlacement;\n\nexport const variationPlacements: Array<VariationPlacement> = basePlacements.reduce(\n  (acc: Array<VariationPlacement>, placement: BasePlacement) =>\n    acc.concat([(`${placement}-${start}`: any), (`${placement}-${end}`: any)]),\n  []\n);\nexport const placements: Array<Placement> = [...basePlacements, auto].reduce(\n  (\n    acc: Array<Placement>,\n    placement: BasePlacement | typeof auto\n  ): Array<Placement> =>\n    acc.concat([\n      placement,\n      (`${placement}-${start}`: any),\n      (`${placement}-${end}`: any),\n    ]),\n  []\n);\n\n// modifiers that need to read the DOM\nexport const beforeRead: 'beforeRead' = 'beforeRead';\nexport const read: 'read' = 'read';\nexport const afterRead: 'afterRead' = 'afterRead';\n// pure-logic modifiers\nexport const beforeMain: 'beforeMain' = 'beforeMain';\nexport const main: 'main' = 'main';\nexport const afterMain: 'afterMain' = 'afterMain';\n// modifier with the purpose to write to the DOM (or write into a framework state)\nexport const beforeWrite: 'beforeWrite' = 'beforeWrite';\nexport const write: 'write' = 'write';\nexport const afterWrite: 'afterWrite' = 'afterWrite';\nexport const modifierPhases: Array<ModifierPhases> = [\n  beforeRead,\n  read,\n  afterRead,\n  beforeMain,\n  main,\n  afterMain,\n  beforeWrite,\n  write,\n  afterWrite,\n];\n\nexport type ModifierPhases =\n  | typeof beforeRead\n  | typeof read\n  | typeof afterRead\n  | typeof beforeMain\n  | typeof main\n  | typeof afterMain\n  | typeof beforeWrite\n  | typeof write\n  | typeof afterWrite;\n", "// @flow\nimport type { Modifier } from '../types';\nimport { modifierPhases } from '../enums';\n\n// source: https://stackoverflow.com/questions/49875255\nfunction order(modifiers) {\n  const map = new Map();\n  const visited = new Set();\n  const result = [];\n\n  modifiers.forEach(modifier => {\n    map.set(modifier.name, modifier);\n  });\n\n  // On visiting object, check for its dependencies and visit them recursively\n  function sort(modifier: Modifier<any, any>) {\n    visited.add(modifier.name);\n\n    const requires = [\n      ...(modifier.requires || []),\n      ...(modifier.requiresIfExists || []),\n    ];\n\n    requires.forEach(dep => {\n      if (!visited.has(dep)) {\n        const depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n\n    result.push(modifier);\n  }\n\n  modifiers.forEach(modifier => {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n\n  return result;\n}\n\nexport default function orderModifiers(\n  modifiers: Array<Modifier<any, any>>\n): Array<Modifier<any, any>> {\n  // order based on dependencies\n  const orderedModifiers = order(modifiers);\n\n  // order based on phase\n  return modifierPhases.reduce((acc, phase) => {\n    return acc.concat(\n      orderedModifiers.filter(modifier => modifier.phase === phase)\n    );\n  }, []);\n}\n", "// @flow\n\nexport default function debounce<T>(fn: Function): () => Promise<T> {\n  let pending;\n  return () => {\n    if (!pending) {\n      pending = new Promise<T>(resolve => {\n        Promise.resolve().then(() => {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}\n", "// @flow\nimport type { Modifier } from '../types';\n\nexport default function mergeByName(\n  modifiers: Array<$Shape<Modifier<any, any>>>\n): Array<$Shape<Modifier<any, any>>> {\n  const merged = modifiers.reduce((merged, current) => {\n    const existing = merged[current.name];\n    merged[current.name] = existing\n      ? {\n          ...existing,\n          ...current,\n          options: { ...existing.options, ...current.options },\n          data: { ...existing.data, ...current.data },\n        }\n      : current;\n    return merged;\n  }, {});\n\n  // IE11 does not support Object.values\n  return Object.keys(merged).map(key => merged[key]);\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport isLayoutViewport from './isLayoutViewport';\nimport type { PositioningStrategy } from '../types';\n\nexport default function getViewportRect(\n  element: Element,\n  strategy: PositioningStrategy\n) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n\n    const layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || (!layoutViewport && strategy === 'fixed')) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width,\n    height,\n    x: x + getWindowScrollBarX(element),\n    y,\n  };\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getWindowScroll from './getWindowScroll';\nimport { max } from '../utils/math';\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\nexport default function getDocumentRect(element: HTMLElement): Rect {\n  const html = getDocumentElement(element);\n  const winScroll = getWindowScroll(element);\n  const body = element.ownerDocument?.body;\n\n  const width = max(\n    html.scrollWidth,\n    html.clientWidth,\n    body ? body.scrollWidth : 0,\n    body ? body.clientWidth : 0\n  );\n  const height = max(\n    html.scrollHeight,\n    html.clientHeight,\n    body ? body.scrollHeight : 0,\n    body ? body.clientHeight : 0\n  );\n\n  let x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return { width, height, x, y };\n}\n", "// @flow\nimport { isShadowRoot } from './instanceOf';\n\nexport default function contains(parent: Element, child: Element) {\n  const rootNode = child.getRootNode && child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n  // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    do {\n      if (next && parent.isSameNode(next)) {\n        return true;\n      }\n      // $FlowFixMe[prop-missing]: need a better way to handle this...\n      next = next.parentNode || next.host;\n    } while (next);\n  }\n\n  // Give up, the result is false\n  return false;\n}\n", "// @flow\nimport type { Rect, ClientRectObject } from '../types';\n\nexport default function rectToClientRect(rect: Rect): ClientRectObject {\n  return {\n    ...rect,\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height,\n  };\n}\n", "// @flow\nimport type { ClientRectObject, PositioningStrategy } from '../types';\nimport type { Boundary, RootBoundary } from '../enums';\nimport { viewport } from '../enums';\nimport getViewportRect from './getViewportRect';\nimport getDocumentRect from './getDocumentRect';\nimport listScrollParents from './listScrollParents';\nimport getOffsetParent from './getOffsetParent';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport { isElement, isHTMLElement } from './instanceOf';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getParentNode from './getParentNode';\nimport contains from './contains';\nimport getNodeName from './getNodeName';\nimport rectToClientRect from '../utils/rectToClientRect';\nimport { max, min } from '../utils/math';\n\nfunction getInnerBoundingClientRect(\n  element: Element,\n  strategy: PositioningStrategy\n) {\n  const rect = getBoundingClientRect(element, false, strategy === 'fixed');\n\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n\n  return rect;\n}\n\nfunction getClientRectFromMixedType(\n  element: Element,\n  clippingParent: Element | RootBoundary,\n  strategy: PositioningStrategy\n): ClientRectObject {\n  return clippingParent === viewport\n    ? rectToClientRect(getViewportRect(element, strategy))\n    : isElement(clippingParent)\n    ? getInnerBoundingClientRect(clippingParent, strategy)\n    : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n}\n\n// A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\nfunction getClippingParents(element: Element): Array<Element> {\n  const clippingParents = listScrollParents(getParentNode(element));\n  const canEscapeClipping =\n    ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  const clipperElement =\n    canEscapeClipping && isHTMLElement(element)\n      ? getOffsetParent(element)\n      : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  }\n\n  // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n  return clippingParents.filter(\n    (clippingParent) =>\n      isElement(clippingParent) &&\n      contains(clippingParent, clipperElement) &&\n      getNodeName(clippingParent) !== 'body'\n  );\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping parents\nexport default function getClippingRect(\n  element: Element,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  strategy: PositioningStrategy\n): ClientRectObject {\n  const mainClippingParents =\n    boundary === 'clippingParents'\n      ? getClippingParents(element)\n      : [].concat(boundary);\n  const clippingParents = [...mainClippingParents, rootBoundary];\n  const firstClippingParent = clippingParents[0];\n\n  const clippingRect = clippingParents.reduce((accRect, clippingParent) => {\n    const rect = getClientRectFromMixedType(element, clippingParent, strategy);\n\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n\n  return clippingRect;\n}\n", "// @flow\nimport { type BasePlacement, type Placement, auto } from '../enums';\n\nexport default function getBasePlacement(\n  placement: Placement | typeof auto\n): BasePlacement {\n  return (placement.split('-')[0]: any);\n}\n", "// @flow\nimport { type Variation, type Placement } from '../enums';\n\nexport default function getVariation(placement: Placement): ?Variation {\n  return (placement.split('-')[1]: any);\n}\n", "// @flow\nimport type { Placement } from '../enums';\n\nexport default function getMainAxisFromPlacement(\n  placement: Placement\n): 'x' | 'y' {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}\n", "// @flow\nimport getBasePlacement from './getBasePlacement';\nimport getVariation from './getVariation';\nimport getMainAxisFromPlacement from './getMainAxisFromPlacement';\nimport type {\n  Rect,\n  PositioningStrategy,\n  Offsets,\n  ClientRectObject,\n} from '../types';\nimport { top, right, bottom, left, start, end, type Placement } from '../enums';\n\nexport default function computeOffsets({\n  reference,\n  element,\n  placement,\n}: {\n  reference: Rect | ClientRectObject,\n  element: Rect | ClientRectObject,\n  strategy: PositioningStrategy,\n  placement?: Placement,\n}): Offsets {\n  const basePlacement = placement ? getBasePlacement(placement) : null;\n  const variation = placement ? getVariation(placement) : null;\n  const commonX = reference.x + reference.width / 2 - element.width / 2;\n  const commonY = reference.y + reference.height / 2 - element.height / 2;\n\n  let offsets;\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height,\n      };\n      break;\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height,\n      };\n      break;\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY,\n      };\n      break;\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY,\n      };\n      break;\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y,\n      };\n  }\n\n  const mainAxis = basePlacement\n    ? getMainAxisFromPlacement(basePlacement)\n    : null;\n\n  if (mainAxis != null) {\n    const len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] =\n          offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n      case end:\n        offsets[mainAxis] =\n          offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n      default:\n    }\n  }\n\n  return offsets;\n}\n", "// @flow\nimport type { SideObject } from '../types';\n\nexport default function getFreshSideObject(): SideObject {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n  };\n}\n", "// @flow\nimport type { SideObject } from '../types';\nimport getFreshSideObject from './getFreshSideObject';\n\nexport default function mergePaddingObject(\n  paddingObject: $Shape<SideObject>\n): SideObject {\n  return {\n    ...getFreshSideObject(),\n    ...paddingObject,\n  };\n}\n", "// @flow\n\nexport default function expandToHashMap<\n  T: number | string | boolean,\n  K: string\n>(value: T, keys: Array<K>): { [key: string]: T } {\n  return keys.reduce((hashMap, key) => {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}\n", "// @flow\nimport type { State, SideObject, Padding, PositioningStrategy } from '../types';\nimport type { Placement, Boundary, RootBoundary, Context } from '../enums';\nimport getClippingRect from '../dom-utils/getClippingRect';\nimport getDocumentElement from '../dom-utils/getDocumentElement';\nimport getBoundingClientRect from '../dom-utils/getBoundingClientRect';\nimport computeOffsets from './computeOffsets';\nimport rectToClientRect from './rectToClientRect';\nimport {\n  clippingParents,\n  reference,\n  popper,\n  bottom,\n  top,\n  right,\n  basePlacements,\n  viewport,\n} from '../enums';\nimport { isElement } from '../dom-utils/instanceOf';\nimport mergePaddingObject from './mergePaddingObject';\nimport expandToHashMap from './expandToHashMap';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  placement: Placement,\n  strategy: PositioningStrategy,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  elementContext: Context,\n  altBoundary: boolean,\n  padding: Padding,\n};\n\nexport default function detectOverflow(\n  state: State,\n  options: $Shape<Options> = {}\n): SideObject {\n  const {\n    placement = state.placement,\n    strategy = state.strategy,\n    boundary = clippingParents,\n    rootBoundary = viewport,\n    elementContext = popper,\n    altBoundary = false,\n    padding = 0,\n  } = options;\n\n  const paddingObject = mergePaddingObject(\n    typeof padding !== 'number'\n      ? padding\n      : expandToHashMap(padding, basePlacements)\n  );\n\n  const altContext = elementContext === popper ? reference : popper;\n\n  const popperRect = state.rects.popper;\n  const element = state.elements[altBoundary ? altContext : elementContext];\n\n  const clippingClientRect = getClippingRect(\n    isElement(element)\n      ? element\n      : element.contextElement || getDocumentElement(state.elements.popper),\n    boundary,\n    rootBoundary,\n    strategy\n  );\n\n  const referenceClientRect = getBoundingClientRect(state.elements.reference);\n\n  const popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement,\n  });\n\n  const popperClientRect = rectToClientRect({\n    ...popperRect,\n    ...popperOffsets,\n  });\n\n  const elementClientRect =\n    elementContext === popper ? popperClientRect : referenceClientRect;\n\n  // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n  const overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom:\n      elementClientRect.bottom -\n      clippingClientRect.bottom +\n      paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right:\n      elementClientRect.right - clippingClientRect.right + paddingObject.right,\n  };\n\n  const offsetData = state.modifiersData.offset;\n\n  // Offsets can be applied only to the popper element\n  if (elementContext === popper && offsetData) {\n    const offset = offsetData[placement];\n\n    Object.keys(overflowOffsets).forEach((key) => {\n      const multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      const axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}\n", "// @flow\nimport type {\n  State,\n  OptionsGeneric,\n  Modifier,\n  Instance,\n  VirtualElement,\n} from './types';\nimport getCompositeRect from './dom-utils/getCompositeRect';\nimport getLayoutRect from './dom-utils/getLayoutRect';\nimport listScrollParents from './dom-utils/listScrollParents';\nimport getOffsetParent from './dom-utils/getOffsetParent';\nimport orderModifiers from './utils/orderModifiers';\nimport debounce from './utils/debounce';\nimport mergeByName from './utils/mergeByName';\nimport detectOverflow from './utils/detectOverflow';\nimport { isElement } from './dom-utils/instanceOf';\n\nconst DEFAULT_OPTIONS: OptionsGeneric<any> = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute',\n};\n\ntype PopperGeneratorArgs = {\n  defaultModifiers?: Array<Modifier<any, any>>,\n  defaultOptions?: $Shape<OptionsGeneric<any>>,\n};\n\nfunction areValidElements(...args: Array<any>): boolean {\n  return !args.some(\n    (element) =>\n      !(element && typeof element.getBoundingClientRect === 'function')\n  );\n}\n\nexport function popperGenerator(generatorOptions: PopperGeneratorArgs = {}) {\n  const { defaultModifiers = [], defaultOptions = DEFAULT_OPTIONS } =\n    generatorOptions;\n\n  return function createPopper<TModifier: $Shape<Modifier<any, any>>>(\n    reference: Element | VirtualElement,\n    popper: HTMLElement,\n    options: $Shape<OptionsGeneric<TModifier>> = defaultOptions\n  ): Instance {\n    let state: $Shape<State> = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: { ...DEFAULT_OPTIONS, ...defaultOptions },\n      modifiersData: {},\n      elements: {\n        reference,\n        popper,\n      },\n      attributes: {},\n      styles: {},\n    };\n\n    let effectCleanupFns: Array<() => void> = [];\n    let isDestroyed = false;\n\n    const instance = {\n      state,\n      setOptions(setOptionsAction) {\n        const options =\n          typeof setOptionsAction === 'function'\n            ? setOptionsAction(state.options)\n            : setOptionsAction;\n\n        cleanupModifierEffects();\n\n        state.options = {\n          // $FlowFixMe[exponential-spread]\n          ...defaultOptions,\n          ...state.options,\n          ...options,\n        };\n\n        state.scrollParents = {\n          reference: isElement(reference)\n            ? listScrollParents(reference)\n            : reference.contextElement\n            ? listScrollParents(reference.contextElement)\n            : [],\n          popper: listScrollParents(popper),\n        };\n\n        // Orders the modifiers based on their dependencies and `phase`\n        // properties\n        const orderedModifiers = orderModifiers(\n          mergeByName([...defaultModifiers, ...state.options.modifiers])\n        );\n\n        // Strip out disabled modifiers\n        state.orderedModifiers = orderedModifiers.filter((m) => m.enabled);\n\n        runModifierEffects();\n\n        return instance.update();\n      },\n\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        const { reference, popper } = state.elements;\n\n        // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n        if (!areValidElements(reference, popper)) {\n          return;\n        }\n\n        // Store the reference and popper rects to be read by modifiers\n        state.rects = {\n          reference: getCompositeRect(\n            reference,\n            getOffsetParent(popper),\n            state.options.strategy === 'fixed'\n          ),\n          popper: getLayoutRect(popper),\n        };\n\n        // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n        state.reset = false;\n\n        state.placement = state.options.placement;\n\n        // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n        state.orderedModifiers.forEach(\n          (modifier) =>\n            (state.modifiersData[modifier.name] = {\n              ...modifier.data,\n            })\n        );\n\n        for (let index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          const { fn, options = {}, name } = state.orderedModifiers[index];\n\n          if (typeof fn === 'function') {\n            state = fn({ state, options, name, instance }) || state;\n          }\n        }\n      },\n\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce<$Shape<State>>(\n        () =>\n          new Promise<$Shape<State>>((resolve) => {\n            instance.forceUpdate();\n            resolve(state);\n          })\n      ),\n\n      destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      },\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then((state) => {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    });\n\n    // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(({ name, options = {}, effect }) => {\n        if (typeof effect === 'function') {\n          const cleanupFn = effect({ state, name, instance, options });\n          const noopFn = () => {};\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach((fn) => fn());\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\n\nexport const createPopper = popperGenerator();\n\n// eslint-disable-next-line import/no-unused-modules\nexport { detectOverflow };\n", "// @flow\nimport type { ModifierArguments, Modifier } from '../types';\nimport getWindow from '../dom-utils/getWindow';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  scroll: boolean,\n  resize: boolean,\n};\n\nconst passive = { passive: true };\n\nfunction effect({ state, instance, options }: ModifierArguments<Options>) {\n  const { scroll = true, resize = true } = options;\n\n  const window = getWindow(state.elements.popper);\n  const scrollParents = [\n    ...state.scrollParents.reference,\n    ...state.scrollParents.popper,\n  ];\n\n  if (scroll) {\n    scrollParents.forEach(scrollParent => {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return () => {\n    if (scroll) {\n      scrollParents.forEach(scrollParent => {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type EventListenersModifier = Modifier<'eventListeners', Options>;\nexport default ({\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: () => {},\n  effect,\n  data: {},\n}: EventListenersModifier);\n", "// @flow\nimport type { ModifierArguments, Modifier } from '../types';\nimport computeOffsets from '../utils/computeOffsets';\n\nfunction popperOffsets({ state, name }: ModifierArguments<{||}>) {\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement,\n  });\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type PopperOffsetsModifier = Modifier<'popperOffsets', {||}>;\nexport default ({\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {},\n}: PopperOffsetsModifier);\n", "// @flow\nimport type {\n  PositioningStrategy,\n  Offsets,\n  Modifier,\n  ModifierArguments,\n  Rect,\n  Window,\n} from '../types';\nimport {\n  type BasePlacement,\n  type Variation,\n  top,\n  left,\n  right,\n  bottom,\n  end,\n} from '../enums';\nimport getOffsetParent from '../dom-utils/getOffsetParent';\nimport getWindow from '../dom-utils/getWindow';\nimport getDocumentElement from '../dom-utils/getDocumentElement';\nimport getComputedStyle from '../dom-utils/getComputedStyle';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getVariation from '../utils/getVariation';\nimport { round } from '../utils/math';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type RoundOffsets = (\n  offsets: $Shape<{ x: number, y: number, centerOffset: number }>\n) => Offsets;\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  gpuAcceleration: boolean,\n  adaptive: boolean,\n  roundOffsets?: boolean | RoundOffsets,\n};\n\nconst unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto',\n};\n\n// Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\nfunction roundOffsetsByDPR({ x, y }, win: Window): Offsets {\n  const dpr = win.devicePixelRatio || 1;\n\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0,\n  };\n}\n\nexport function mapToStyles({\n  popper,\n  popperRect,\n  placement,\n  variation,\n  offsets,\n  position,\n  gpuAcceleration,\n  adaptive,\n  roundOffsets,\n  isFixed,\n}: {\n  popper: HTMLElement,\n  popperRect: Rect,\n  placement: BasePlacement,\n  variation: ?Variation,\n  offsets: $Shape<{ x: number, y: number, centerOffset: number }>,\n  position: PositioningStrategy,\n  gpuAcceleration: boolean,\n  adaptive: boolean,\n  roundOffsets: boolean | RoundOffsets,\n  isFixed: boolean,\n}) {\n  let { x = 0, y = 0 } = offsets;\n\n  ({ x, y } =\n    typeof roundOffsets === 'function' ? roundOffsets({ x, y }) : { x, y });\n\n  const hasX = offsets.hasOwnProperty('x');\n  const hasY = offsets.hasOwnProperty('y');\n\n  let sideX: string = left;\n  let sideY: string = top;\n\n  const win: Window = window;\n\n  if (adaptive) {\n    let offsetParent = getOffsetParent(popper);\n    let heightProp = 'clientHeight';\n    let widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (\n        getComputedStyle(offsetParent).position !== 'static' &&\n        position === 'absolute'\n      ) {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    }\n\n    // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n    offsetParent = (offsetParent: Element);\n\n    if (\n      placement === top ||\n      ((placement === left || placement === right) && variation === end)\n    ) {\n      sideY = bottom;\n      const offsetY =\n        isFixed && offsetParent === win && win.visualViewport\n          ? win.visualViewport.height\n          : // $FlowFixMe[prop-missing]\n            offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (\n      placement === left ||\n      ((placement === top || placement === bottom) && variation === end)\n    ) {\n      sideX = right;\n      const offsetX =\n        isFixed && offsetParent === win && win.visualViewport\n          ? win.visualViewport.width\n          : // $FlowFixMe[prop-missing]\n            offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  const commonStyles = {\n    position,\n    ...(adaptive && unsetSides),\n  };\n\n  ({ x, y } =\n    roundOffsets === true\n      ? roundOffsetsByDPR({ x, y }, getWindow(popper))\n      : { x, y });\n\n  if (gpuAcceleration) {\n    return {\n      ...commonStyles,\n      [sideY]: hasY ? '0' : '',\n      [sideX]: hasX ? '0' : '',\n      // Layer acceleration can disable subpixel rendering which causes slightly\n      // blurry text on low PPI displays, so we want to use 2D transforms\n      // instead\n      transform:\n        (win.devicePixelRatio || 1) <= 1\n          ? `translate(${x}px, ${y}px)`\n          : `translate3d(${x}px, ${y}px, 0)`,\n    };\n  }\n\n  return {\n    ...commonStyles,\n    [sideY]: hasY ? `${y}px` : '',\n    [sideX]: hasX ? `${x}px` : '',\n    transform: '',\n  };\n}\n\nfunction computeStyles({ state, options }: ModifierArguments<Options>) {\n  const {\n    gpuAcceleration = true,\n    adaptive = true,\n    // defaults to use builtin `roundOffsetsByDPR`\n    roundOffsets = true,\n  } = options;\n\n  const commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed',\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = {\n      ...state.styles.popper,\n      ...mapToStyles({\n        ...commonStyles,\n        offsets: state.modifiersData.popperOffsets,\n        position: state.options.strategy,\n        adaptive,\n        roundOffsets,\n      }),\n    };\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = {\n      ...state.styles.arrow,\n      ...mapToStyles({\n        ...commonStyles,\n        offsets: state.modifiersData.arrow,\n        position: 'absolute',\n        adaptive: false,\n        roundOffsets,\n      }),\n    };\n  }\n\n  state.attributes.popper = {\n    ...state.attributes.popper,\n    'data-popper-placement': state.placement,\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ComputeStylesModifier = Modifier<'computeStyles', Options>;\nexport default ({\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {},\n}: ComputeStylesModifier);\n", "// @flow\nimport type { Modifier, ModifierArguments } from '../types';\nimport getNodeName from '../dom-utils/getNodeName';\nimport { isHTMLElement } from '../dom-utils/instanceOf';\n\n// This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles({ state }: ModifierArguments<{||}>) {\n  Object.keys(state.elements).forEach((name) => {\n    const style = state.styles[name] || {};\n\n    const attributes = state.attributes[name] || {};\n    const element = state.elements[name];\n\n    // arrow is optional + virtual elements\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    }\n\n    // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n    Object.assign(element.style, style);\n\n    Object.keys(attributes).forEach((name) => {\n      const value = attributes[name];\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect({ state }: ModifierArguments<{||}>) {\n  const initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0',\n    },\n    arrow: {\n      position: 'absolute',\n    },\n    reference: {},\n  };\n\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return () => {\n    Object.keys(state.elements).forEach((name) => {\n      const element = state.elements[name];\n      const attributes = state.attributes[name] || {};\n\n      const styleProperties = Object.keys(\n        state.styles.hasOwnProperty(name)\n          ? state.styles[name]\n          : initialStyles[name]\n      );\n\n      // Set all values to an empty string to unset them\n      const style = styleProperties.reduce((style, property) => {\n        style[property] = '';\n        return style;\n      }, {});\n\n      // arrow is optional + virtual elements\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n\n      Object.keys(attributes).forEach((attribute) => {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ApplyStylesModifier = Modifier<'applyStyles', {||}>;\nexport default ({\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect,\n  requires: ['computeStyles'],\n}: ApplyStylesModifier);\n", "// @flow\nimport type { Placement } from '../enums';\nimport type { ModifierArguments, Modifier, Rect, Offsets } from '../types';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport { top, left, right, placements } from '../enums';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type OffsetsFunction = ({\n  popper: Rect,\n  reference: Rect,\n  placement: Placement,\n}) => [?number, ?number];\n\ntype Offset = OffsetsFunction | [?number, ?number];\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  offset: Offset,\n};\n\nexport function distanceAndSkiddingToXY(\n  placement: Placement,\n  rects: { popper: Rect, reference: Rect },\n  offset: Offset\n): Offsets {\n  const basePlacement = getBasePlacement(placement);\n  const invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  let [skidding, distance] =\n    typeof offset === 'function'\n      ? offset({\n          ...rects,\n          placement,\n        })\n      : offset;\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n\n  return [left, right].indexOf(basePlacement) >= 0\n    ? { x: distance, y: skidding }\n    : { x: skidding, y: distance };\n}\n\nfunction offset({ state, options, name }: ModifierArguments<Options>) {\n  const { offset = [0, 0] } = options;\n\n  const data = placements.reduce((acc, placement) => {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n\n  const { x, y } = data[state.placement];\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type OffsetModifier = Modifier<'offset', Options>;\nexport default ({\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset,\n}: OffsetModifier);\n", "// @flow\nimport type { Placement } from '../enums';\n\nconst hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n\nexport default function getOppositePlacement(placement: Placement): Placement {\n  return (placement.replace(\n    /left|right|bottom|top/g,\n    matched => hash[matched]\n  ): any);\n}\n", "// @flow\nimport type { Placement } from '../enums';\n\nconst hash = { start: 'end', end: 'start' };\n\nexport default function getOppositeVariationPlacement(\n  placement: Placement\n): Placement {\n  return (placement.replace(/start|end/g, matched => hash[matched]): any);\n}\n", "// @flow\nimport type { State, Padding } from '../types';\nimport type {\n  Placement,\n  ComputedPlacement,\n  Boundary,\n  RootBoundary,\n} from '../enums';\nimport getVariation from './getVariation';\nimport {\n  variationPlacements,\n  basePlacements,\n  placements as allPlacements,\n} from '../enums';\nimport detectOverflow from './detectOverflow';\nimport getBasePlacement from './getBasePlacement';\n\ntype Options = {\n  placement: Placement,\n  padding: Padding,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  flipVariations: boolean,\n  allowedAutoPlacements?: Array<Placement>,\n};\n\ntype OverflowsMap = { [ComputedPlacement]: number };\n\nexport default function computeAutoPlacement(\n  state: $Shape<State>,\n  options: Options = {}\n): Array<ComputedPlacement> {\n  const {\n    placement,\n    boundary,\n    rootBoundary,\n    padding,\n    flipVariations,\n    allowedAutoPlacements = allPlacements,\n  } = options;\n\n  const variation = getVariation(placement);\n\n  const placements = variation\n    ? flipVariations\n      ? variationPlacements\n      : variationPlacements.filter(\n          (placement) => getVariation(placement) === variation\n        )\n    : basePlacements;\n\n  let allowedPlacements = placements.filter(\n    (placement) => allowedAutoPlacements.indexOf(placement) >= 0\n  );\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  }\n\n  // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n  const overflows: OverflowsMap = allowedPlacements.reduce((acc, placement) => {\n    acc[placement] = detectOverflow(state, {\n      placement,\n      boundary,\n      rootBoundary,\n      padding,\n    })[getBasePlacement(placement)];\n\n    return acc;\n  }, {});\n\n  return Object.keys(overflows).sort((a, b) => overflows[a] - overflows[b]);\n}\n", "// @flow\nimport type { Placement, Boundary, RootBoundary } from '../enums';\nimport type { ModifierArguments, Modifier, Padding } from '../types';\nimport getOppositePlacement from '../utils/getOppositePlacement';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getOppositeVariationPlacement from '../utils/getOppositeVariationPlacement';\nimport detectOverflow from '../utils/detectOverflow';\nimport computeAutoPlacement from '../utils/computeAutoPlacement';\nimport { bottom, top, start, right, left, auto } from '../enums';\nimport getVariation from '../utils/getVariation';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  mainAxis: boolean,\n  altAxis: boolean,\n  fallbackPlacements: Array<Placement>,\n  padding: Padding,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  altBoundary: boolean,\n  flipVariations: boolean,\n  allowedAutoPlacements: Array<Placement>,\n};\n\nfunction getExpandedFallbackPlacements(placement: Placement): Array<Placement> {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  const oppositePlacement = getOppositePlacement(placement);\n\n  return [\n    getOppositeVariationPlacement(placement),\n    oppositePlacement,\n    getOppositeVariationPlacement(oppositePlacement),\n  ];\n}\n\nfunction flip({ state, options, name }: ModifierArguments<Options>) {\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  const {\n    mainAxis: checkMainAxis = true,\n    altAxis: checkAltAxis = true,\n    fallbackPlacements: specifiedFallbackPlacements,\n    padding,\n    boundary,\n    rootBoundary,\n    altBoundary,\n    flipVariations = true,\n    allowedAutoPlacements,\n  } = options;\n\n  const preferredPlacement = state.options.placement;\n  const basePlacement = getBasePlacement(preferredPlacement);\n  const isBasePlacement = basePlacement === preferredPlacement;\n\n  const fallbackPlacements =\n    specifiedFallbackPlacements ||\n    (isBasePlacement || !flipVariations\n      ? [getOppositePlacement(preferredPlacement)]\n      : getExpandedFallbackPlacements(preferredPlacement));\n\n  const placements = [preferredPlacement, ...fallbackPlacements].reduce(\n    (acc, placement) => {\n      return acc.concat(\n        getBasePlacement(placement) === auto\n          ? computeAutoPlacement(state, {\n              placement,\n              boundary,\n              rootBoundary,\n              padding,\n              flipVariations,\n              allowedAutoPlacements,\n            })\n          : placement\n      );\n    },\n    []\n  );\n\n  const referenceRect = state.rects.reference;\n  const popperRect = state.rects.popper;\n\n  const checksMap = new Map();\n  let makeFallbackChecks = true;\n  let firstFittingPlacement = placements[0];\n\n  for (let i = 0; i < placements.length; i++) {\n    const placement = placements[i];\n    const basePlacement = getBasePlacement(placement);\n    const isStartVariation = getVariation(placement) === start;\n    const isVertical = [top, bottom].indexOf(basePlacement) >= 0;\n    const len = isVertical ? 'width' : 'height';\n\n    const overflow = detectOverflow(state, {\n      placement,\n      boundary,\n      rootBoundary,\n      altBoundary,\n      padding,\n    });\n\n    let mainVariationSide: any = isVertical\n      ? isStartVariation\n        ? right\n        : left\n      : isStartVariation\n      ? bottom\n      : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    const altVariationSide: any = getOppositePlacement(mainVariationSide);\n\n    const checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(\n        overflow[mainVariationSide] <= 0,\n        overflow[altVariationSide] <= 0\n      );\n    }\n\n    if (checks.every((check) => check)) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    const numberOfChecks = flipVariations ? 3 : 1;\n\n    for (let i = numberOfChecks; i > 0; i--) {\n      const fittingPlacement = placements.find((placement) => {\n        const checks = checksMap.get(placement);\n        if (checks) {\n          return checks.slice(0, i).every((check) => check);\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        break;\n      }\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type FlipModifier = Modifier<'flip', Options>;\nexport default ({\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: { _skip: false },\n}: FlipModifier);\n", "// @flow\n\nexport default function getAltAxis(axis: 'x' | 'y'): 'x' | 'y' {\n  return axis === 'x' ? 'y' : 'x';\n}\n", "// @flow\nimport { max as mathMax, min as mathMin } from './math';\n\nexport function within(min: number, value: number, max: number): number {\n  return mathMax(min, mathMin(value, max));\n}\n\nexport function withinMaxClamp(min: number, value: number, max: number) {\n  const v = within(min, value, max);\n  return v > max ? max : v;\n}\n", "// @flow\nimport { top, left, right, bottom, start } from '../enums';\nimport type { Placement, Boundary, RootBoundary } from '../enums';\nimport type { Rect, ModifierArguments, Modifier, Padding } from '../types';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getMainAxisFromPlacement from '../utils/getMainAxisFromPlacement';\nimport getAltAxis from '../utils/getAltAxis';\nimport { within, withinMaxClamp } from '../utils/within';\nimport getLayoutRect from '../dom-utils/getLayoutRect';\nimport getOffsetParent from '../dom-utils/getOffsetParent';\nimport detectOverflow from '../utils/detectOverflow';\nimport getVariation from '../utils/getVariation';\nimport getFreshSideObject from '../utils/getFreshSideObject';\nimport { min as mathMin, max as mathMax } from '../utils/math';\n\ntype TetherOffset =\n  | (({\n      popper: Rect,\n      reference: Rect,\n      placement: Placement,\n    }) => number | { mainAxis: number, altAxis: number })\n  | number\n  | { mainAxis: number, altAxis: number };\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  /* Prevents boundaries overflow on the main axis */\n  mainAxis: boolean,\n  /* Prevents boundaries overflow on the alternate axis */\n  altAxis: boolean,\n  /* The area to check the popper is overflowing in */\n  boundary: Boundary,\n  /* If the popper is not overflowing the main area, fallback to this one */\n  rootBoundary: RootBoundary,\n  /* Use the reference's \"clippingParents\" boundary context */\n  altBoundary: boolean,\n  /**\n   * Allows the popper to overflow from its boundaries to keep it near its\n   * reference element\n   */\n  tether: boolean,\n  /* Offsets when the `tether` option should activate */\n  tetherOffset: TetherOffset,\n  /* Sets a padding to the provided boundary */\n  padding: Padding,\n};\n\nfunction preventOverflow({ state, options, name }: ModifierArguments<Options>) {\n  const {\n    mainAxis: checkMainAxis = true,\n    altAxis: checkAltAxis = false,\n    boundary,\n    rootBoundary,\n    altBoundary,\n    padding,\n    tether = true,\n    tetherOffset = 0,\n  } = options;\n\n  const overflow = detectOverflow(state, {\n    boundary,\n    rootBoundary,\n    padding,\n    altBoundary,\n  });\n  const basePlacement = getBasePlacement(state.placement);\n  const variation = getVariation(state.placement);\n  const isBasePlacement = !variation;\n  const mainAxis = getMainAxisFromPlacement(basePlacement);\n  const altAxis = getAltAxis(mainAxis);\n  const popperOffsets = state.modifiersData.popperOffsets;\n  const referenceRect = state.rects.reference;\n  const popperRect = state.rects.popper;\n  const tetherOffsetValue =\n    typeof tetherOffset === 'function'\n      ? tetherOffset({\n          ...state.rects,\n          placement: state.placement,\n        })\n      : tetherOffset;\n  const normalizedTetherOffsetValue =\n    typeof tetherOffsetValue === 'number'\n      ? { mainAxis: tetherOffsetValue, altAxis: tetherOffsetValue }\n      : { mainAxis: 0, altAxis: 0, ...tetherOffsetValue };\n  const offsetModifierState = state.modifiersData.offset\n    ? state.modifiersData.offset[state.placement]\n    : null;\n\n  const data = { x: 0, y: 0 };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    const mainSide = mainAxis === 'y' ? top : left;\n    const altSide = mainAxis === 'y' ? bottom : right;\n    const len = mainAxis === 'y' ? 'height' : 'width';\n    const offset = popperOffsets[mainAxis];\n\n    const min = offset + overflow[mainSide];\n    const max = offset - overflow[altSide];\n\n    const additive = tether ? -popperRect[len] / 2 : 0;\n\n    const minLen = variation === start ? referenceRect[len] : popperRect[len];\n    const maxLen = variation === start ? -popperRect[len] : -referenceRect[len];\n\n    // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n    const arrowElement = state.elements.arrow;\n    const arrowRect =\n      tether && arrowElement\n        ? getLayoutRect(arrowElement)\n        : { width: 0, height: 0 };\n    const arrowPaddingObject = state.modifiersData['arrow#persistent']\n      ? state.modifiersData['arrow#persistent'].padding\n      : getFreshSideObject();\n    const arrowPaddingMin = arrowPaddingObject[mainSide];\n    const arrowPaddingMax = arrowPaddingObject[altSide];\n\n    // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n    const arrowLen = within(0, referenceRect[len], arrowRect[len]);\n\n    const minOffset = isBasePlacement\n      ? referenceRect[len] / 2 -\n        additive -\n        arrowLen -\n        arrowPaddingMin -\n        normalizedTetherOffsetValue.mainAxis\n      : minLen -\n        arrowLen -\n        arrowPaddingMin -\n        normalizedTetherOffsetValue.mainAxis;\n    const maxOffset = isBasePlacement\n      ? -referenceRect[len] / 2 +\n        additive +\n        arrowLen +\n        arrowPaddingMax +\n        normalizedTetherOffsetValue.mainAxis\n      : maxLen +\n        arrowLen +\n        arrowPaddingMax +\n        normalizedTetherOffsetValue.mainAxis;\n\n    const arrowOffsetParent =\n      state.elements.arrow && getOffsetParent(state.elements.arrow);\n    const clientOffset = arrowOffsetParent\n      ? mainAxis === 'y'\n        ? arrowOffsetParent.clientTop || 0\n        : arrowOffsetParent.clientLeft || 0\n      : 0;\n\n    const offsetModifierValue = offsetModifierState?.[mainAxis] ?? 0;\n    const tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    const tetherMax = offset + maxOffset - offsetModifierValue;\n\n    const preventedOffset = within(\n      tether ? mathMin(min, tetherMin) : min,\n      offset,\n      tether ? mathMax(max, tetherMax) : max\n    );\n\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    const mainSide = mainAxis === 'x' ? top : left;\n    const altSide = mainAxis === 'x' ? bottom : right;\n    const offset = popperOffsets[altAxis];\n\n    const len = altAxis === 'y' ? 'height' : 'width';\n\n    const min = offset + overflow[mainSide];\n    const max = offset - overflow[altSide];\n\n    const isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    const offsetModifierValue = offsetModifierState?.[altAxis] ?? 0;\n    const tetherMin = isOriginSide\n      ? min\n      : offset -\n        referenceRect[len] -\n        popperRect[len] -\n        offsetModifierValue +\n        normalizedTetherOffsetValue.altAxis;\n    const tetherMax = isOriginSide\n      ? offset +\n        referenceRect[len] +\n        popperRect[len] -\n        offsetModifierValue -\n        normalizedTetherOffsetValue.altAxis\n      : max;\n\n    const preventedOffset =\n      tether && isOriginSide\n        ? withinMaxClamp(tetherMin, offset, tetherMax)\n        : within(tether ? tetherMin : min, offset, tether ? tetherMax : max);\n\n    popperOffsets[altAxis] = preventedOffset;\n    data[altAxis] = preventedOffset - offset;\n  }\n\n  state.modifiersData[name] = data;\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type PreventOverflowModifier = Modifier<'preventOverflow', Options>;\nexport default ({\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset'],\n}: PreventOverflowModifier);\n", "// @flow\nimport type { Modifier, ModifierArguments, Padding, Rect } from '../types';\nimport type { Placement } from '../enums';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getLayoutRect from '../dom-utils/getLayoutRect';\nimport contains from '../dom-utils/contains';\nimport getOffsetParent from '../dom-utils/getOffsetParent';\nimport getMainAxisFromPlacement from '../utils/getMainAxisFromPlacement';\nimport { within } from '../utils/within';\nimport mergePaddingObject from '../utils/mergePaddingObject';\nimport expandToHashMap from '../utils/expandToHashMap';\nimport { left, right, basePlacements, top, bottom } from '../enums';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  element: HTMLElement | string | null,\n  padding:\n    | Padding\n    | (({|\n        popper: Rect,\n        reference: Rect,\n        placement: Placement,\n      |}) => Padding),\n};\n\nconst toPaddingObject = (padding, state) => {\n  padding =\n    typeof padding === 'function'\n      ? padding({ ...state.rects, placement: state.placement })\n      : padding;\n\n  return mergePaddingObject(\n    typeof padding !== 'number'\n      ? padding\n      : expandToHashMap(padding, basePlacements)\n  );\n};\n\nfunction arrow({ state, name, options }: ModifierArguments<Options>) {\n  const arrowElement = state.elements.arrow;\n  const popperOffsets = state.modifiersData.popperOffsets;\n  const basePlacement = getBasePlacement(state.placement);\n  const axis = getMainAxisFromPlacement(basePlacement);\n  const isVertical = [left, right].indexOf(basePlacement) >= 0;\n  const len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  const paddingObject = toPaddingObject(options.padding, state);\n  const arrowRect = getLayoutRect(arrowElement);\n  const minProp = axis === 'y' ? top : left;\n  const maxProp = axis === 'y' ? bottom : right;\n\n  const endDiff =\n    state.rects.reference[len] +\n    state.rects.reference[axis] -\n    popperOffsets[axis] -\n    state.rects.popper[len];\n  const startDiff = popperOffsets[axis] - state.rects.reference[axis];\n\n  const arrowOffsetParent = getOffsetParent(arrowElement);\n  const clientSize = arrowOffsetParent\n    ? axis === 'y'\n      ? arrowOffsetParent.clientHeight || 0\n      : arrowOffsetParent.clientWidth || 0\n    : 0;\n\n  const centerToReference = endDiff / 2 - startDiff / 2;\n\n  // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n  const min = paddingObject[minProp];\n  const max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  const center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  const offset = within(min, center, max);\n\n  // Prevents breaking syntax highlighting...\n  const axisProp: string = axis;\n  state.modifiersData[name] = {\n    [axisProp]: offset,\n    centerOffset: offset - center,\n  };\n}\n\nfunction effect({ state, options }: ModifierArguments<Options>) {\n  let { element: arrowElement = '[data-popper-arrow]' } = options;\n\n  if (arrowElement == null) {\n    return;\n  }\n\n  // CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ArrowModifier = Modifier<'arrow', Options>;\nexport default ({\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow'],\n}: ArrowModifier);\n", "// @flow\nimport type {\n  ModifierArguments,\n  Modifier,\n  Rect,\n  SideObject,\n  Offsets,\n} from '../types';\nimport { top, bottom, left, right } from '../enums';\nimport detectOverflow from '../utils/detectOverflow';\n\nfunction getSideOffsets(\n  overflow: SideObject,\n  rect: Rect,\n  preventedOffsets: Offsets = { x: 0, y: 0 }\n): SideObject {\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x,\n  };\n}\n\nfunction isAnySideFullyClipped(overflow: SideObject): boolean {\n  return [top, right, bottom, left].some((side) => overflow[side] >= 0);\n}\n\nfunction hide({ state, name }: ModifierArguments<{||}>) {\n  const referenceRect = state.rects.reference;\n  const popperRect = state.rects.popper;\n  const preventedOffsets = state.modifiersData.preventOverflow;\n\n  const referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference',\n  });\n  const popperAltOverflow = detectOverflow(state, {\n    altBoundary: true,\n  });\n\n  const referenceClippingOffsets = getSideOffsets(\n    referenceOverflow,\n    referenceRect\n  );\n  const popperEscapeOffsets = getSideOffsets(\n    popperAltOverflow,\n    popperRect,\n    preventedOffsets\n  );\n\n  const isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  const hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n\n  state.modifiersData[name] = {\n    referenceClippingOffsets,\n    popperEscapeOffsets,\n    isReferenceHidden,\n    hasPopperEscaped,\n  };\n\n  state.attributes.popper = {\n    ...state.attributes.popper,\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped,\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type HideModifier = Modifier<'hide', {||}>;\nexport default ({\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide,\n}: HideModifier);\n", "// @flow\nimport { popperGenerator, detectOverflow } from './createPopper';\n\nimport eventListeners from './modifiers/eventListeners';\nimport popperOffsets from './modifiers/popperOffsets';\nimport computeStyles from './modifiers/computeStyles';\nimport applyStyles from './modifiers/applyStyles';\n\nexport type * from './types';\n\nconst defaultModifiers = [\n  eventListeners,\n  popperOffsets,\n  computeStyles,\n  applyStyles,\n];\n\nconst createPopper = popperGenerator({ defaultModifiers });\n\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };\n", "// @flow\nimport { popperGenerator, detectOverflow } from './createPopper';\n\nimport eventListeners from './modifiers/eventListeners';\nimport popperOffsets from './modifiers/popperOffsets';\nimport computeStyles from './modifiers/computeStyles';\nimport applyStyles from './modifiers/applyStyles';\nimport offset from './modifiers/offset';\nimport flip from './modifiers/flip';\nimport preventOverflow from './modifiers/preventOverflow';\nimport arrow from './modifiers/arrow';\nimport hide from './modifiers/hide';\n\nexport type * from './types';\n\nconst defaultModifiers = [\n  eventListeners,\n  popperOffsets,\n  computeStyles,\n  applyStyles,\n  offset,\n  flip,\n  preventOverflow,\n  arrow,\n  hide,\n];\n\nconst createPopper = popperGenerator({ defaultModifiers });\n\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper as createPopperLite } from './popper-lite';\n// eslint-disable-next-line import/no-unused-modules\nexport * from './modifiers';\n"], "names": ["getWindow", "node", "window", "toString", "ownerDocument", "defaultView", "isElement", "OwnElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "max", "Math", "min", "round", "getUAString", "uaData", "navigator", "userAgentData", "brands", "Array", "isArray", "map", "item", "brand", "version", "join", "userAgent", "isLayoutViewport", "test", "getBoundingClientRect", "element", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "offsetHeight", "height", "visualViewport", "addVisualOffsets", "x", "left", "offsetLeft", "y", "top", "offsetTop", "right", "bottom", "getWindowScroll", "win", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "getHTMLElementScroll", "getNodeScroll", "getNodeName", "nodeName", "toLowerCase", "getDocumentElement", "document", "documentElement", "getWindowScrollBarX", "getComputedStyle", "isScrollParent", "overflow", "overflowX", "overflowY", "isElementScaled", "rect", "getCompositeRect", "elementOrVirtualElement", "offsetParent", "isFixed", "isOffsetParentAnElement", "offsetParentIsScaled", "scroll", "offsets", "clientLeft", "clientTop", "getLayoutRect", "abs", "getParentNode", "assignedSlot", "parentNode", "host", "getScrollParent", "indexOf", "body", "listScrollParents", "list", "scrollParent", "isBody", "target", "concat", "updatedList", "isTableElement", "getTrueOffsetParent", "position", "getContainingBlock", "isFirefox", "isIE", "elementCss", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "filter", "getOffsetParent", "auto", "basePlacements", "start", "end", "clippingParents", "viewport", "popper", "reference", "variationPlacements", "reduce", "acc", "placement", "placements", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases", "order", "modifiers", "Map", "visited", "Set", "result", "for<PERSON>ach", "modifier", "set", "name", "sort", "add", "requires", "requiresIfExists", "dep", "has", "depModifier", "get", "push", "orderModifiers", "orderedModifiers", "phase", "debounce", "fn", "pending", "Promise", "resolve", "then", "undefined", "mergeByName", "merged", "current", "existing", "options", "data", "Object", "keys", "key", "getViewportRect", "strategy", "html", "clientWidth", "clientHeight", "layoutViewport", "getDocumentRect", "winScroll", "scrollWidth", "scrollHeight", "direction", "contains", "parent", "child", "rootNode", "getRootNode", "next", "isSameNode", "rectToClientRect", "getInnerBoundingClientRect", "getClientRectFromMixedType", "clippingParent", "getClippingParents", "canEscapeClipping", "clipperElement", "getClippingRect", "boundary", "rootBoundary", "mainClippingParents", "firstClippingParent", "clippingRect", "accRect", "getBasePlacement", "split", "getVariation", "getMainAxisFromPlacement", "computeOffsets", "basePlacement", "variation", "commonX", "commonY", "mainAxis", "len", "getFreshSideObject", "mergePaddingObject", "paddingObject", "expandToHashMap", "value", "hashMap", "detectOverflow", "state", "elementContext", "altBoundary", "padding", "altContext", "popperRect", "rects", "elements", "clippingClientRect", "contextElement", "referenceClientRect", "popperOffsets", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "modifiersData", "offset", "multiply", "axis", "DEFAULT_OPTIONS", "areValidElements", "args", "some", "popperGenerator", "generatorOptions", "defaultModifiers", "defaultOptions", "createPopper", "attributes", "styles", "effectCleanupFns", "isDestroyed", "instance", "setOptions", "setOptionsAction", "cleanupModifierEffects", "scrollParents", "m", "enabled", "runModifierEffects", "update", "forceUpdate", "reset", "index", "length", "destroy", "onFirstUpdate", "effect", "cleanupFn", "noopFn", "passive", "resize", "addEventListener", "removeEventListener", "unsetSides", "roundOffsetsByDPR", "dpr", "devicePixelRatio", "mapToStyles", "gpuAcceleration", "adaptive", "roundOffsets", "hasX", "hasOwnProperty", "hasY", "sideX", "sideY", "heightProp", "widthProp", "offsetY", "offsetX", "commonStyles", "computeStyles", "arrow", "applyStyles", "style", "assign", "removeAttribute", "setAttribute", "initialStyles", "margin", "styleProperties", "property", "attribute", "distanceAndSkiddingToXY", "invertDistance", "skidding", "distance", "hash", "getOppositePlacement", "replace", "matched", "getOppositeVariationPlacement", "computeAutoPlacement", "flipVariations", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "a", "b", "getExpandedFallbackPlacements", "oppositePlacement", "flip", "_skip", "checkMainAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "preferredPlacement", "isBasePlacement", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "i", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "numberOfChecks", "fittingPlacement", "find", "slice", "getAltAxis", "within", "mathMax", "mathMin", "withinMaxClamp", "v", "preventOverflow", "tether", "tetherOffset", "tetherOffsetValue", "normalizedTetherOffsetValue", "offsetModifierState", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowElement", "arrowRect", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "arrowOffsetParent", "clientOffset", "offsetModifierValue", "tetherMin", "tetherMax", "preventedOffset", "isOriginSide", "toPaddingObject", "minProp", "maxProp", "endDiff", "startDiff", "clientSize", "centerToReference", "center", "axisProp", "centerOffset", "querySelector", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "side", "hide", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "eventListeners"], "mappings": ";;;;;;;;;;EAIe,SAASA,SAAT,CAAmBC,IAAnB,EAAyB;EACtC,MAAIA,IAAI,IAAI,IAAZ,EAAkB;EAChB,WAAOC,MAAP;EACD;;EAED,MAAID,IAAI,CAACE,QAAL,OAAoB,iBAAxB,EAA2C;EACzC,QAAMC,aAAa,GAAGH,IAAI,CAACG,aAA3B;EACA,WAAOA,aAAa,GAAGA,aAAa,CAACC,WAAd,IAA6BH,MAAhC,GAAyCA,MAA7D;EACD;;EAED,SAAOD,IAAP;EACD;;ECVD,SAASK,SAAT,CAAmBL,IAAnB,EAAyB;EACvB,MAAMM,UAAU,GAAGP,SAAS,CAACC,IAAD,CAAT,CAAgBO,OAAnC;EACA,SAAOP,IAAI,YAAYM,UAAhB,IAA8BN,IAAI,YAAYO,OAArD;EACD;;EAID,SAASC,aAAT,CAAuBR,IAAvB,EAA6B;EAC3B,MAAMM,UAAU,GAAGP,SAAS,CAACC,IAAD,CAAT,CAAgBS,WAAnC;EACA,SAAOT,IAAI,YAAYM,UAAhB,IAA8BN,IAAI,YAAYS,WAArD;EACD;;EAID,SAASC,YAAT,CAAsBV,IAAtB,EAA4B;EAC1B;EACA,MAAI,OAAOW,UAAP,KAAsB,WAA1B,EAAuC;EACrC,WAAO,KAAP;EACD;;EACD,MAAML,UAAU,GAAGP,SAAS,CAACC,IAAD,CAAT,CAAgBW,UAAnC;EACA,SAAOX,IAAI,YAAYM,UAAhB,IAA8BN,IAAI,YAAYW,UAArD;EACD;;ECzBM,IAAMC,GAAG,GAAGC,IAAI,CAACD,GAAjB;EACA,IAAME,GAAG,GAAGD,IAAI,CAACC,GAAjB;EACA,IAAMC,KAAK,GAAGF,IAAI,CAACE,KAAnB;;ECMQ,SAASC,WAAT,GAA+B;EAC5C,MAAMC,MAAM,GAAIC,SAAD,CAAuBC,aAAtC;;EAEA,MAAIF,MAAM,QAAN,IAAAA,MAAM,CAAEG,MAAR,IAAkBC,KAAK,CAACC,OAAN,CAAcL,MAAM,CAACG,MAArB,CAAtB,EAAoD;EAClD,WAAOH,MAAM,CAACG,MAAP,CACJG,GADI,CACA,UAACC,IAAD;EAAA,aAAaA,IAAI,CAACC,KAAlB,SAA2BD,IAAI,CAACE,OAAhC;EAAA,KADA,EAEJC,IAFI,CAEC,GAFD,CAAP;EAGD;;EAED,SAAOT,SAAS,CAACU,SAAjB;EACD;;EChBc,SAASC,gBAAT,GAA4B;EACzC,SAAO,CAAC,iCAAiCC,IAAjC,CAAsCd,WAAW,EAAjD,CAAR;EACD;;ECEc,SAASe,qBAAT,CACbC,OADa,EAEbC,YAFa,EAGbC,eAHa,EAIK;EAAA,MAFlBD,YAEkB;EAFlBA,IAAAA,YAEkB,GAFM,KAEN;EAAA;;EAAA,MADlBC,eACkB;EADlBA,IAAAA,eACkB,GADS,KACT;EAAA;;EAClB,MAAMC,UAAU,GAAGH,OAAO,CAACD,qBAAR,EAAnB;EACA,MAAIK,MAAM,GAAG,CAAb;EACA,MAAIC,MAAM,GAAG,CAAb;;EAEA,MAAIJ,YAAY,IAAIzB,aAAa,CAACwB,OAAD,CAAjC,EAA4C;EAC1CI,IAAAA,MAAM,GACHJ,OAAD,CAAuBM,WAAvB,GAAqC,CAArC,GACIvB,KAAK,CAACoB,UAAU,CAACI,KAAZ,CAAL,GAA2BP,OAAD,CAAuBM,WAAjD,IAAgE,CADpE,GAEI,CAHN;EAIAD,IAAAA,MAAM,GACHL,OAAD,CAAuBQ,YAAvB,GAAsC,CAAtC,GACIzB,KAAK,CAACoB,UAAU,CAACM,MAAZ,CAAL,GAA4BT,OAAD,CAAuBQ,YAAlD,IAAkE,CADtE,GAEI,CAHN;EAID;;EAdiB,aAgBSnC,SAAS,CAAC2B,OAAD,CAAT,GAAqBjC,SAAS,CAACiC,OAAD,CAA9B,GAA0C/B,MAhBnD;EAAA,MAgBVyC,cAhBU,QAgBVA,cAhBU;;EAiBlB,MAAMC,gBAAgB,GAAG,CAACd,gBAAgB,EAAjB,IAAuBK,eAAhD;EAEA,MAAMU,CAAC,GACL,CAACT,UAAU,CAACU,IAAX,IACEF,gBAAgB,IAAID,cAApB,GAAqCA,cAAc,CAACI,UAApD,GAAiE,CADnE,CAAD,IAEAV,MAHF;EAIA,MAAMW,CAAC,GACL,CAACZ,UAAU,CAACa,GAAX,IACEL,gBAAgB,IAAID,cAApB,GAAqCA,cAAc,CAACO,SAApD,GAAgE,CADlE,CAAD,IAEAZ,MAHF;EAIA,MAAME,KAAK,GAAGJ,UAAU,CAACI,KAAX,GAAmBH,MAAjC;EACA,MAAMK,MAAM,GAAGN,UAAU,CAACM,MAAX,GAAoBJ,MAAnC;EAEA,SAAO;EACLE,IAAAA,KAAK,EAALA,KADK;EAELE,IAAAA,MAAM,EAANA,MAFK;EAGLO,IAAAA,GAAG,EAAED,CAHA;EAILG,IAAAA,KAAK,EAAEN,CAAC,GAAGL,KAJN;EAKLY,IAAAA,MAAM,EAAEJ,CAAC,GAAGN,MALP;EAMLI,IAAAA,IAAI,EAAED,CAND;EAOLA,IAAAA,CAAC,EAADA,CAPK;EAQLG,IAAAA,CAAC,EAADA;EARK,GAAP;EAUD;;EC/Cc,SAASK,eAAT,CAAyBpD,IAAzB,EAA8C;EAC3D,MAAMqD,GAAG,GAAGtD,SAAS,CAACC,IAAD,CAArB;EACA,MAAMsD,UAAU,GAAGD,GAAG,CAACE,WAAvB;EACA,MAAMC,SAAS,GAAGH,GAAG,CAACI,WAAtB;EAEA,SAAO;EACLH,IAAAA,UAAU,EAAVA,UADK;EAELE,IAAAA,SAAS,EAATA;EAFK,GAAP;EAID;;ECXc,SAASE,oBAAT,CAA8B1B,OAA9B,EAAoD;EACjE,SAAO;EACLsB,IAAAA,UAAU,EAAEtB,OAAO,CAACsB,UADf;EAELE,IAAAA,SAAS,EAAExB,OAAO,CAACwB;EAFd,GAAP;EAID;;ECAc,SAASG,aAAT,CAAuB3D,IAAvB,EAA4C;EACzD,MAAIA,IAAI,KAAKD,SAAS,CAACC,IAAD,CAAlB,IAA4B,CAACQ,aAAa,CAACR,IAAD,CAA9C,EAAsD;EACpD,WAAOoD,eAAe,CAACpD,IAAD,CAAtB;EACD,GAFD,MAEO;EACL,WAAO0D,oBAAoB,CAAC1D,IAAD,CAA3B;EACD;EACF;;ECVc,SAAS4D,WAAT,CAAqB5B,OAArB,EAAuD;EACpE,SAAOA,OAAO,GAAG,CAACA,OAAO,CAAC6B,QAAR,IAAoB,EAArB,EAAyBC,WAAzB,EAAH,GAA4C,IAA1D;EACD;;ECDc,SAASC,kBAAT,CACb/B,OADa,EAEA;EACb;EACA,SAAO,CACL,CAAC3B,SAAS,CAAC2B,OAAD,CAAT,GACGA,OAAO,CAAC7B,aADX;EAGG6B,EAAAA,OAAO,CAACgC,QAHZ,KAGyB/D,MAAM,CAAC+D,QAJ3B,EAKLC,eALF;EAMD;;ECTc,SAASC,mBAAT,CAA6BlC,OAA7B,EAAuD;EACpE;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SACED,qBAAqB,CAACgC,kBAAkB,CAAC/B,OAAD,CAAnB,CAArB,CAAmDa,IAAnD,GACAO,eAAe,CAACpB,OAAD,CAAf,CAAyBsB,UAF3B;EAID;;ECdc,SAASa,gBAAT,CACbnC,OADa,EAEQ;EACrB,SAAOjC,SAAS,CAACiC,OAAD,CAAT,CAAmBmC,gBAAnB,CAAoCnC,OAApC,CAAP;EACD;;ECJc,SAASoC,cAAT,CAAwBpC,OAAxB,EAAuD;EACpE;EADoE,0BAEzBmC,gBAAgB,CAACnC,OAAD,CAFS;EAAA,MAE5DqC,QAF4D,qBAE5DA,QAF4D;EAAA,MAElDC,SAFkD,qBAElDA,SAFkD;EAAA,MAEvCC,SAFuC,qBAEvCA,SAFuC;;EAGpE,SAAO,6BAA6BzC,IAA7B,CAAkCuC,QAAQ,GAAGE,SAAX,GAAuBD,SAAzD,CAAP;EACD;;ECID,SAASE,eAAT,CAAyBxC,OAAzB,EAA+C;EAC7C,MAAMyC,IAAI,GAAGzC,OAAO,CAACD,qBAAR,EAAb;EACA,MAAMK,MAAM,GAAGrB,KAAK,CAAC0D,IAAI,CAAClC,KAAN,CAAL,GAAoBP,OAAO,CAACM,WAA5B,IAA2C,CAA1D;EACA,MAAMD,MAAM,GAAGtB,KAAK,CAAC0D,IAAI,CAAChC,MAAN,CAAL,GAAqBT,OAAO,CAACQ,YAA7B,IAA6C,CAA5D;EAEA,SAAOJ,MAAM,KAAK,CAAX,IAAgBC,MAAM,KAAK,CAAlC;EACD;EAGD;;;EACe,SAASqC,gBAAT,CACbC,uBADa,EAEbC,YAFa,EAGbC,OAHa,EAIP;EAAA,MADNA,OACM;EADNA,IAAAA,OACM,GADa,KACb;EAAA;;EACN,MAAMC,uBAAuB,GAAGtE,aAAa,CAACoE,YAAD,CAA7C;EACA,MAAMG,oBAAoB,GACxBvE,aAAa,CAACoE,YAAD,CAAb,IAA+BJ,eAAe,CAACI,YAAD,CADhD;EAEA,MAAMX,eAAe,GAAGF,kBAAkB,CAACa,YAAD,CAA1C;EACA,MAAMH,IAAI,GAAG1C,qBAAqB,CAChC4C,uBADgC,EAEhCI,oBAFgC,EAGhCF,OAHgC,CAAlC;EAMA,MAAIG,MAAM,GAAG;EAAE1B,IAAAA,UAAU,EAAE,CAAd;EAAiBE,IAAAA,SAAS,EAAE;EAA5B,GAAb;EACA,MAAIyB,OAAO,GAAG;EAAErC,IAAAA,CAAC,EAAE,CAAL;EAAQG,IAAAA,CAAC,EAAE;EAAX,GAAd;;EAEA,MAAI+B,uBAAuB,IAAK,CAACA,uBAAD,IAA4B,CAACD,OAA7D,EAAuE;EACrE,QACEjB,WAAW,CAACgB,YAAD,CAAX,KAA8B,MAA9B;EAEAR,IAAAA,cAAc,CAACH,eAAD,CAHhB,EAIE;EACAe,MAAAA,MAAM,GAAGrB,aAAa,CAACiB,YAAD,CAAtB;EACD;;EAED,QAAIpE,aAAa,CAACoE,YAAD,CAAjB,EAAiC;EAC/BK,MAAAA,OAAO,GAAGlD,qBAAqB,CAAC6C,YAAD,EAAe,IAAf,CAA/B;EACAK,MAAAA,OAAO,CAACrC,CAAR,IAAagC,YAAY,CAACM,UAA1B;EACAD,MAAAA,OAAO,CAAClC,CAAR,IAAa6B,YAAY,CAACO,SAA1B;EACD,KAJD,MAIO,IAAIlB,eAAJ,EAAqB;EAC1BgB,MAAAA,OAAO,CAACrC,CAAR,GAAYsB,mBAAmB,CAACD,eAAD,CAA/B;EACD;EACF;;EAED,SAAO;EACLrB,IAAAA,CAAC,EAAE6B,IAAI,CAAC5B,IAAL,GAAYmC,MAAM,CAAC1B,UAAnB,GAAgC2B,OAAO,CAACrC,CADtC;EAELG,IAAAA,CAAC,EAAE0B,IAAI,CAACzB,GAAL,GAAWgC,MAAM,CAACxB,SAAlB,GAA8ByB,OAAO,CAAClC,CAFpC;EAGLR,IAAAA,KAAK,EAAEkC,IAAI,CAAClC,KAHP;EAILE,IAAAA,MAAM,EAAEgC,IAAI,CAAChC;EAJR,GAAP;EAMD;;EC1DD;;EACe,SAAS2C,aAAT,CAAuBpD,OAAvB,EAAmD;EAChE,MAAMG,UAAU,GAAGJ,qBAAqB,CAACC,OAAD,CAAxC,CADgE;EAIhE;;EACA,MAAIO,KAAK,GAAGP,OAAO,CAACM,WAApB;EACA,MAAIG,MAAM,GAAGT,OAAO,CAACQ,YAArB;;EAEA,MAAI3B,IAAI,CAACwE,GAAL,CAASlD,UAAU,CAACI,KAAX,GAAmBA,KAA5B,KAAsC,CAA1C,EAA6C;EAC3CA,IAAAA,KAAK,GAAGJ,UAAU,CAACI,KAAnB;EACD;;EAED,MAAI1B,IAAI,CAACwE,GAAL,CAASlD,UAAU,CAACM,MAAX,GAAoBA,MAA7B,KAAwC,CAA5C,EAA+C;EAC7CA,IAAAA,MAAM,GAAGN,UAAU,CAACM,MAApB;EACD;;EAED,SAAO;EACLG,IAAAA,CAAC,EAAEZ,OAAO,CAACc,UADN;EAELC,IAAAA,CAAC,EAAEf,OAAO,CAACiB,SAFN;EAGLV,IAAAA,KAAK,EAALA,KAHK;EAILE,IAAAA,MAAM,EAANA;EAJK,GAAP;EAMD;;ECvBc,SAAS6C,aAAT,CAAuBtD,OAAvB,EAAyD;EACtE,MAAI4B,WAAW,CAAC5B,OAAD,CAAX,KAAyB,MAA7B,EAAqC;EACnC,WAAOA,OAAP;EACD;;EAED;EAEE;EACA;EACAA,IAAAA,OAAO,CAACuD,YAAR;EACAvD,IAAAA,OAAO,CAACwD,UADR;EAEC9E,IAAAA,YAAY,CAACsB,OAAD,CAAZ,GAAwBA,OAAO,CAACyD,IAAhC,GAAuC,IAFxC;EAGA;EACA1B,IAAAA,kBAAkB,CAAC/B,OAAD,CARpB;;EAAA;EAUD;;ECdc,SAAS0D,eAAT,CAAyB1F,IAAzB,EAAkD;EAC/D,MAAI,CAAC,MAAD,EAAS,MAAT,EAAiB,WAAjB,EAA8B2F,OAA9B,CAAsC/B,WAAW,CAAC5D,IAAD,CAAjD,KAA4D,CAAhE,EAAmE;EACjE;EACA,WAAOA,IAAI,CAACG,aAAL,CAAmByF,IAA1B;EACD;;EAED,MAAIpF,aAAa,CAACR,IAAD,CAAb,IAAuBoE,cAAc,CAACpE,IAAD,CAAzC,EAAiD;EAC/C,WAAOA,IAAP;EACD;;EAED,SAAO0F,eAAe,CAACJ,aAAa,CAACtF,IAAD,CAAd,CAAtB;EACD;;ECVD;EACA;EACA;EACA;EACA;EACA;;EACe,SAAS6F,iBAAT,CACb7D,OADa,EAEb8D,IAFa,EAG6B;EAAA;;EAAA,MAD1CA,IAC0C;EAD1CA,IAAAA,IAC0C,GADV,EACU;EAAA;;EAC1C,MAAMC,YAAY,GAAGL,eAAe,CAAC1D,OAAD,CAApC;EACA,MAAMgE,MAAM,GAAGD,YAAY,+BAAK/D,OAAO,CAAC7B,aAAb,qBAAK,sBAAuByF,IAA5B,CAA3B;EACA,MAAMvC,GAAG,GAAGtD,SAAS,CAACgG,YAAD,CAArB;EACA,MAAME,MAAM,GAAGD,MAAM,GACjB,CAAC3C,GAAD,EAAM6C,MAAN,CACE7C,GAAG,CAACX,cAAJ,IAAsB,EADxB,EAEE0B,cAAc,CAAC2B,YAAD,CAAd,GAA+BA,YAA/B,GAA8C,EAFhD,CADiB,GAKjBA,YALJ;EAMA,MAAMI,WAAW,GAAGL,IAAI,CAACI,MAAL,CAAYD,MAAZ,CAApB;EAEA,SAAOD,MAAM,GACTG,WADS;EAGTA,EAAAA,WAAW,CAACD,MAAZ,CAAmBL,iBAAiB,CAACP,aAAa,CAACW,MAAD,CAAd,CAApC,CAHJ;EAID;;EC7Bc,SAASG,cAAT,CAAwBpE,OAAxB,EAAmD;EAChE,SAAO,CAAC,OAAD,EAAU,IAAV,EAAgB,IAAhB,EAAsB2D,OAAtB,CAA8B/B,WAAW,CAAC5B,OAAD,CAAzC,KAAuD,CAA9D;EACD;;ECID,SAASqE,mBAAT,CAA6BrE,OAA7B,EAAyD;EACvD,MACE,CAACxB,aAAa,CAACwB,OAAD,CAAd;EAEAmC,EAAAA,gBAAgB,CAACnC,OAAD,CAAhB,CAA0BsE,QAA1B,KAAuC,OAHzC,EAIE;EACA,WAAO,IAAP;EACD;;EAED,SAAOtE,OAAO,CAAC4C,YAAf;EACD;EAGD;;;EACA,SAAS2B,kBAAT,CAA4BvE,OAA5B,EAA8C;EAC5C,MAAMwE,SAAS,GAAG,WAAW1E,IAAX,CAAgBd,WAAW,EAA3B,CAAlB;EACA,MAAMyF,IAAI,GAAG,WAAW3E,IAAX,CAAgBd,WAAW,EAA3B,CAAb;;EAEA,MAAIyF,IAAI,IAAIjG,aAAa,CAACwB,OAAD,CAAzB,EAAoC;EAClC;EACA,QAAM0E,UAAU,GAAGvC,gBAAgB,CAACnC,OAAD,CAAnC;;EACA,QAAI0E,UAAU,CAACJ,QAAX,KAAwB,OAA5B,EAAqC;EACnC,aAAO,IAAP;EACD;EACF;;EAED,MAAIK,WAAW,GAAGrB,aAAa,CAACtD,OAAD,CAA/B;;EAEA,MAAItB,YAAY,CAACiG,WAAD,CAAhB,EAA+B;EAC7BA,IAAAA,WAAW,GAAGA,WAAW,CAAClB,IAA1B;EACD;;EAED,SACEjF,aAAa,CAACmG,WAAD,CAAb,IACA,CAAC,MAAD,EAAS,MAAT,EAAiBhB,OAAjB,CAAyB/B,WAAW,CAAC+C,WAAD,CAApC,IAAqD,CAFvD,EAGE;EACA,QAAMC,GAAG,GAAGzC,gBAAgB,CAACwC,WAAD,CAA5B,CADA;EAIA;EACA;;EACA,QACEC,GAAG,CAACC,SAAJ,KAAkB,MAAlB,IACAD,GAAG,CAACE,WAAJ,KAAoB,MADpB,IAEAF,GAAG,CAACG,OAAJ,KAAgB,OAFhB,IAGA,CAAC,WAAD,EAAc,aAAd,EAA6BpB,OAA7B,CAAqCiB,GAAG,CAACI,UAAzC,MAAyD,CAAC,CAH1D,IAICR,SAAS,IAAII,GAAG,CAACI,UAAJ,KAAmB,QAJjC,IAKCR,SAAS,IAAII,GAAG,CAACK,MAAjB,IAA2BL,GAAG,CAACK,MAAJ,KAAe,MAN7C,EAOE;EACA,aAAON,WAAP;EACD,KATD,MASO;EACLA,MAAAA,WAAW,GAAGA,WAAW,CAACnB,UAA1B;EACD;EACF;;EAED,SAAO,IAAP;EACD;EAGD;;;EACe,SAAS0B,eAAT,CAAyBlF,OAAzB,EAA2C;EACxD,MAAM/B,MAAM,GAAGF,SAAS,CAACiC,OAAD,CAAxB;EAEA,MAAI4C,YAAY,GAAGyB,mBAAmB,CAACrE,OAAD,CAAtC;;EAEA,SACE4C,YAAY,IACZwB,cAAc,CAACxB,YAAD,CADd,IAEAT,gBAAgB,CAACS,YAAD,CAAhB,CAA+B0B,QAA/B,KAA4C,QAH9C,EAIE;EACA1B,IAAAA,YAAY,GAAGyB,mBAAmB,CAACzB,YAAD,CAAlC;EACD;;EAED,MACEA,YAAY,KACXhB,WAAW,CAACgB,YAAD,CAAX,KAA8B,MAA9B,IACEhB,WAAW,CAACgB,YAAD,CAAX,KAA8B,MAA9B,IACCT,gBAAgB,CAACS,YAAD,CAAhB,CAA+B0B,QAA/B,KAA4C,QAHpC,CADd,EAKE;EACA,WAAOrG,MAAP;EACD;;EAED,SAAO2E,YAAY,IAAI2B,kBAAkB,CAACvE,OAAD,CAAlC,IAA+C/B,MAAtD;EACD;;EC3FM,IAAM+C,GAAU,GAAG,KAAnB;EACA,IAAMG,MAAgB,GAAG,QAAzB;EACA,IAAMD,KAAc,GAAG,OAAvB;EACA,IAAML,IAAY,GAAG,MAArB;EACA,IAAMsE,IAAY,GAAG,MAArB;EAMA,IAAMC,cAAoC,GAAG,CAACpE,GAAD,EAAMG,MAAN,EAAcD,KAAd,EAAqBL,IAArB,CAA7C;EAEA,IAAMwE,KAAc,GAAG,OAAvB;EACA,IAAMC,GAAU,GAAG,KAAnB;EAGA,IAAMC,eAAkC,GAAG,iBAA3C;EACA,IAAMC,QAAoB,GAAG,UAA7B;EAIA,IAAMC,MAAgB,GAAG,QAAzB;EACA,IAAMC,SAAsB,GAAG,WAA/B;EAgBA,IAAMC,mBAA8C,gBAAGP,cAAc,CAACQ,MAAf,CAC5D,UAACC,GAAD,EAAiCC,SAAjC;EAAA,SACED,GAAG,CAAC3B,MAAJ,CAAW,CAAK4B,SAAL,SAAkBT,KAAlB,EAAqCS,SAArC,SAAkDR,GAAlD,CAAX,CADF;EAAA,CAD4D,EAG5D,EAH4D,CAAvD;EAKA,IAAMS,UAA4B,gBAAG,UAAIX,cAAJ,GAAoBD,IAApB,GAA0BS,MAA1B,CAC1C,UACEC,GADF,EAEEC,SAFF;EAAA,SAIED,GAAG,CAAC3B,MAAJ,CAAW,CACT4B,SADS,EAELA,SAFK,SAEQT,KAFR,EAGLS,SAHK,SAGQR,GAHR,CAAX,CAJF;EAAA,CAD0C,EAU1C,EAV0C,CAArC;;EAcA,IAAMU,UAAwB,GAAG,YAAjC;EACA,IAAMC,IAAY,GAAG,MAArB;EACA,IAAMC,SAAsB,GAAG,WAA/B;;EAEA,IAAMC,UAAwB,GAAG,YAAjC;EACA,IAAMC,IAAY,GAAG,MAArB;EACA,IAAMC,SAAsB,GAAG,WAA/B;;EAEA,IAAMC,WAA0B,GAAG,aAAnC;EACA,IAAMC,KAAc,GAAG,OAAvB;EACA,IAAMC,UAAwB,GAAG,YAAjC;EACA,IAAMC,cAAqC,GAAG,CACnDT,UADmD,EAEnDC,IAFmD,EAGnDC,SAHmD,EAInDC,UAJmD,EAKnDC,IALmD,EAMnDC,SANmD,EAOnDC,WAPmD,EAQnDC,KARmD,EASnDC,UATmD,CAA9C;;EChEP,SAASE,KAAT,CAAeC,SAAf,EAA0B;EACxB,MAAMpH,GAAG,GAAG,IAAIqH,GAAJ,EAAZ;EACA,MAAMC,OAAO,GAAG,IAAIC,GAAJ,EAAhB;EACA,MAAMC,MAAM,GAAG,EAAf;EAEAJ,EAAAA,SAAS,CAACK,OAAV,CAAkB,UAAAC,QAAQ,EAAI;EAC5B1H,IAAAA,GAAG,CAAC2H,GAAJ,CAAQD,QAAQ,CAACE,IAAjB,EAAuBF,QAAvB;EACD,GAFD,EALwB;;EAUxB,WAASG,IAAT,CAAcH,QAAd,EAA4C;EAC1CJ,IAAAA,OAAO,CAACQ,GAAR,CAAYJ,QAAQ,CAACE,IAArB;EAEA,QAAMG,QAAQ,aACRL,QAAQ,CAACK,QAAT,IAAqB,EADb,EAERL,QAAQ,CAACM,gBAAT,IAA6B,EAFrB,CAAd;EAKAD,IAAAA,QAAQ,CAACN,OAAT,CAAiB,UAAAQ,GAAG,EAAI;EACtB,UAAI,CAACX,OAAO,CAACY,GAAR,CAAYD,GAAZ,CAAL,EAAuB;EACrB,YAAME,WAAW,GAAGnI,GAAG,CAACoI,GAAJ,CAAQH,GAAR,CAApB;;EAEA,YAAIE,WAAJ,EAAiB;EACfN,UAAAA,IAAI,CAACM,WAAD,CAAJ;EACD;EACF;EACF,KARD;EAUAX,IAAAA,MAAM,CAACa,IAAP,CAAYX,QAAZ;EACD;;EAEDN,EAAAA,SAAS,CAACK,OAAV,CAAkB,UAAAC,QAAQ,EAAI;EAC5B,QAAI,CAACJ,OAAO,CAACY,GAAR,CAAYR,QAAQ,CAACE,IAArB,CAAL,EAAiC;EAC/B;EACAC,MAAAA,IAAI,CAACH,QAAD,CAAJ;EACD;EACF,GALD;EAOA,SAAOF,MAAP;EACD;;EAEc,SAASc,cAAT,CACblB,SADa,EAEc;EAC3B;EACA,MAAMmB,gBAAgB,GAAGpB,KAAK,CAACC,SAAD,CAA9B,CAF2B;;EAK3B,SAAOF,cAAc,CAACb,MAAf,CAAsB,UAACC,GAAD,EAAMkC,KAAN,EAAgB;EAC3C,WAAOlC,GAAG,CAAC3B,MAAJ,CACL4D,gBAAgB,CAAC7C,MAAjB,CAAwB,UAAAgC,QAAQ;EAAA,aAAIA,QAAQ,CAACc,KAAT,KAAmBA,KAAvB;EAAA,KAAhC,CADK,CAAP;EAGD,GAJM,EAIJ,EAJI,CAAP;EAKD;;ECxDc,SAASC,QAAT,CAAqBC,EAArB,EAAqD;EAClE,MAAIC,OAAJ;EACA,SAAO,YAAM;EACX,QAAI,CAACA,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAIC,OAAJ,CAAe,UAAAC,OAAO,EAAI;EAClCD,QAAAA,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,YAAM;EAC3BH,UAAAA,OAAO,GAAGI,SAAV;EACAF,UAAAA,OAAO,CAACH,EAAE,EAAH,CAAP;EACD,SAHD;EAID,OALS,CAAV;EAMD;;EAED,WAAOC,OAAP;EACD,GAXD;EAYD;;ECbc,SAASK,WAAT,CACb5B,SADa,EAEsB;EACnC,MAAM6B,MAAM,GAAG7B,SAAS,CAACf,MAAV,CAAiB,UAAC4C,MAAD,EAASC,OAAT,EAAqB;EACnD,QAAMC,QAAQ,GAAGF,MAAM,CAACC,OAAO,CAACtB,IAAT,CAAvB;EACAqB,IAAAA,MAAM,CAACC,OAAO,CAACtB,IAAT,CAAN,GAAuBuB,QAAQ,qBAEtBA,QAFsB,EAGtBD,OAHsB;EAIzBE,MAAAA,OAAO,oBAAOD,QAAQ,CAACC,OAAhB,EAA4BF,OAAO,CAACE,OAApC,CAJkB;EAKzBC,MAAAA,IAAI,oBAAOF,QAAQ,CAACE,IAAhB,EAAyBH,OAAO,CAACG,IAAjC;EALqB,SAO3BH,OAPJ;EAQA,WAAOD,MAAP;EACD,GAXc,EAWZ,EAXY,CAAf,CADmC;;EAenC,SAAOK,MAAM,CAACC,IAAP,CAAYN,MAAZ,EAAoBjJ,GAApB,CAAwB,UAAAwJ,GAAG;EAAA,WAAIP,MAAM,CAACO,GAAD,CAAV;EAAA,GAA3B,CAAP;EACD;;ECdc,SAASC,eAAT,CACbhJ,OADa,EAEbiJ,QAFa,EAGb;EACA,MAAM5H,GAAG,GAAGtD,SAAS,CAACiC,OAAD,CAArB;EACA,MAAMkJ,IAAI,GAAGnH,kBAAkB,CAAC/B,OAAD,CAA/B;EACA,MAAMU,cAAc,GAAGW,GAAG,CAACX,cAA3B;EAEA,MAAIH,KAAK,GAAG2I,IAAI,CAACC,WAAjB;EACA,MAAI1I,MAAM,GAAGyI,IAAI,CAACE,YAAlB;EACA,MAAIxI,CAAC,GAAG,CAAR;EACA,MAAIG,CAAC,GAAG,CAAR;;EAEA,MAAIL,cAAJ,EAAoB;EAClBH,IAAAA,KAAK,GAAGG,cAAc,CAACH,KAAvB;EACAE,IAAAA,MAAM,GAAGC,cAAc,CAACD,MAAxB;EAEA,QAAM4I,cAAc,GAAGxJ,gBAAgB,EAAvC;;EAEA,QAAIwJ,cAAc,IAAK,CAACA,cAAD,IAAmBJ,QAAQ,KAAK,OAAvD,EAAiE;EAC/DrI,MAAAA,CAAC,GAAGF,cAAc,CAACI,UAAnB;EACAC,MAAAA,CAAC,GAAGL,cAAc,CAACO,SAAnB;EACD;EACF;;EAED,SAAO;EACLV,IAAAA,KAAK,EAALA,KADK;EAELE,IAAAA,MAAM,EAANA,MAFK;EAGLG,IAAAA,CAAC,EAAEA,CAAC,GAAGsB,mBAAmB,CAAClC,OAAD,CAHrB;EAILe,IAAAA,CAAC,EAADA;EAJK,GAAP;EAMD;;EC7BD;;EACe,SAASuI,eAAT,CAAyBtJ,OAAzB,EAAqD;EAAA;;EAClE,MAAMkJ,IAAI,GAAGnH,kBAAkB,CAAC/B,OAAD,CAA/B;EACA,MAAMuJ,SAAS,GAAGnI,eAAe,CAACpB,OAAD,CAAjC;EACA,MAAM4D,IAAI,4BAAG5D,OAAO,CAAC7B,aAAX,qBAAG,sBAAuByF,IAApC;EAEA,MAAMrD,KAAK,GAAG3B,GAAG,CACfsK,IAAI,CAACM,WADU,EAEfN,IAAI,CAACC,WAFU,EAGfvF,IAAI,GAAGA,IAAI,CAAC4F,WAAR,GAAsB,CAHX,EAIf5F,IAAI,GAAGA,IAAI,CAACuF,WAAR,GAAsB,CAJX,CAAjB;EAMA,MAAM1I,MAAM,GAAG7B,GAAG,CAChBsK,IAAI,CAACO,YADW,EAEhBP,IAAI,CAACE,YAFW,EAGhBxF,IAAI,GAAGA,IAAI,CAAC6F,YAAR,GAAuB,CAHX,EAIhB7F,IAAI,GAAGA,IAAI,CAACwF,YAAR,GAAuB,CAJX,CAAlB;EAOA,MAAIxI,CAAC,GAAG,CAAC2I,SAAS,CAACjI,UAAX,GAAwBY,mBAAmB,CAAClC,OAAD,CAAnD;EACA,MAAMe,CAAC,GAAG,CAACwI,SAAS,CAAC/H,SAArB;;EAEA,MAAIW,gBAAgB,CAACyB,IAAI,IAAIsF,IAAT,CAAhB,CAA+BQ,SAA/B,KAA6C,KAAjD,EAAwD;EACtD9I,IAAAA,CAAC,IAAIhC,GAAG,CAACsK,IAAI,CAACC,WAAN,EAAmBvF,IAAI,GAAGA,IAAI,CAACuF,WAAR,GAAsB,CAA7C,CAAH,GAAqD5I,KAA1D;EACD;;EAED,SAAO;EAAEA,IAAAA,KAAK,EAALA,KAAF;EAASE,IAAAA,MAAM,EAANA,MAAT;EAAiBG,IAAAA,CAAC,EAADA,CAAjB;EAAoBG,IAAAA,CAAC,EAADA;EAApB,GAAP;EACD;;ECjCc,SAAS4I,QAAT,CAAkBC,MAAlB,EAAmCC,KAAnC,EAAmD;EAChE,MAAMC,QAAQ,GAAGD,KAAK,CAACE,WAAN,IAAqBF,KAAK,CAACE,WAAN,EAAtC,CADgE;;EAIhE,MAAIH,MAAM,CAACD,QAAP,CAAgBE,KAAhB,CAAJ,EAA4B;EAC1B,WAAO,IAAP;EACD,GAFD;EAAA,OAIK,IAAIC,QAAQ,IAAIpL,YAAY,CAACoL,QAAD,CAA5B,EAAwC;EAC3C,UAAIE,IAAI,GAAGH,KAAX;;EACA,SAAG;EACD,YAAIG,IAAI,IAAIJ,MAAM,CAACK,UAAP,CAAkBD,IAAlB,CAAZ,EAAqC;EACnC,iBAAO,IAAP;EACD,SAHA;;;EAKDA,QAAAA,IAAI,GAAGA,IAAI,CAACxG,UAAL,IAAmBwG,IAAI,CAACvG,IAA/B;EACD,OAND,QAMSuG,IANT;EAOD,KAjB+D;;;EAoBhE,SAAO,KAAP;EACD;;ECrBc,SAASE,gBAAT,CAA0BzH,IAA1B,EAAwD;EACrE,2BACKA,IADL;EAEE5B,IAAAA,IAAI,EAAE4B,IAAI,CAAC7B,CAFb;EAGEI,IAAAA,GAAG,EAAEyB,IAAI,CAAC1B,CAHZ;EAIEG,IAAAA,KAAK,EAAEuB,IAAI,CAAC7B,CAAL,GAAS6B,IAAI,CAAClC,KAJvB;EAKEY,IAAAA,MAAM,EAAEsB,IAAI,CAAC1B,CAAL,GAAS0B,IAAI,CAAChC;EALxB;EAOD;;ECOD,SAAS0J,0BAAT,CACEnK,OADF,EAEEiJ,QAFF,EAGE;EACA,MAAMxG,IAAI,GAAG1C,qBAAqB,CAACC,OAAD,EAAU,KAAV,EAAiBiJ,QAAQ,KAAK,OAA9B,CAAlC;EAEAxG,EAAAA,IAAI,CAACzB,GAAL,GAAWyB,IAAI,CAACzB,GAAL,GAAWhB,OAAO,CAACmD,SAA9B;EACAV,EAAAA,IAAI,CAAC5B,IAAL,GAAY4B,IAAI,CAAC5B,IAAL,GAAYb,OAAO,CAACkD,UAAhC;EACAT,EAAAA,IAAI,CAACtB,MAAL,GAAcsB,IAAI,CAACzB,GAAL,GAAWhB,OAAO,CAACoJ,YAAjC;EACA3G,EAAAA,IAAI,CAACvB,KAAL,GAAauB,IAAI,CAAC5B,IAAL,GAAYb,OAAO,CAACmJ,WAAjC;EACA1G,EAAAA,IAAI,CAAClC,KAAL,GAAaP,OAAO,CAACmJ,WAArB;EACA1G,EAAAA,IAAI,CAAChC,MAAL,GAAcT,OAAO,CAACoJ,YAAtB;EACA3G,EAAAA,IAAI,CAAC7B,CAAL,GAAS6B,IAAI,CAAC5B,IAAd;EACA4B,EAAAA,IAAI,CAAC1B,CAAL,GAAS0B,IAAI,CAACzB,GAAd;EAEA,SAAOyB,IAAP;EACD;;EAED,SAAS2H,0BAAT,CACEpK,OADF,EAEEqK,cAFF,EAGEpB,QAHF,EAIoB;EAClB,SAAOoB,cAAc,KAAK7E,QAAnB,GACH0E,gBAAgB,CAAClB,eAAe,CAAChJ,OAAD,EAAUiJ,QAAV,CAAhB,CADb,GAEH5K,SAAS,CAACgM,cAAD,CAAT,GACAF,0BAA0B,CAACE,cAAD,EAAiBpB,QAAjB,CAD1B,GAEAiB,gBAAgB,CAACZ,eAAe,CAACvH,kBAAkB,CAAC/B,OAAD,CAAnB,CAAhB,CAJpB;EAKD;EAGD;EACA;;;EACA,SAASsK,kBAAT,CAA4BtK,OAA5B,EAA8D;EAC5D,MAAMuF,eAAe,GAAG1B,iBAAiB,CAACP,aAAa,CAACtD,OAAD,CAAd,CAAzC;EACA,MAAMuK,iBAAiB,GACrB,CAAC,UAAD,EAAa,OAAb,EAAsB5G,OAAtB,CAA8BxB,gBAAgB,CAACnC,OAAD,CAAhB,CAA0BsE,QAAxD,KAAqE,CADvE;EAEA,MAAMkG,cAAc,GAClBD,iBAAiB,IAAI/L,aAAa,CAACwB,OAAD,CAAlC,GACIkF,eAAe,CAAClF,OAAD,CADnB,GAEIA,OAHN;;EAKA,MAAI,CAAC3B,SAAS,CAACmM,cAAD,CAAd,EAAgC;EAC9B,WAAO,EAAP;EACD,GAX2D;;;EAc5D,SAAOjF,eAAe,CAACN,MAAhB,CACL,UAACoF,cAAD;EAAA,WACEhM,SAAS,CAACgM,cAAD,CAAT,IACAV,QAAQ,CAACU,cAAD,EAAiBG,cAAjB,CADR,IAEA5I,WAAW,CAACyI,cAAD,CAAX,KAAgC,MAHlC;EAAA,GADK,CAAP;EAMD;EAGD;;;EACe,SAASI,eAAT,CACbzK,OADa,EAEb0K,QAFa,EAGbC,YAHa,EAIb1B,QAJa,EAKK;EAClB,MAAM2B,mBAAmB,GACvBF,QAAQ,KAAK,iBAAb,GACIJ,kBAAkB,CAACtK,OAAD,CADtB,GAEI,GAAGkE,MAAH,CAAUwG,QAAV,CAHN;EAIA,MAAMnF,eAAe,aAAOqF,mBAAP,GAA4BD,YAA5B,EAArB;EACA,MAAME,mBAAmB,GAAGtF,eAAe,CAAC,CAAD,CAA3C;EAEA,MAAMuF,YAAY,GAAGvF,eAAe,CAACK,MAAhB,CAAuB,UAACmF,OAAD,EAAUV,cAAV,EAA6B;EACvE,QAAM5H,IAAI,GAAG2H,0BAA0B,CAACpK,OAAD,EAAUqK,cAAV,EAA0BpB,QAA1B,CAAvC;EAEA8B,IAAAA,OAAO,CAAC/J,GAAR,GAAcpC,GAAG,CAAC6D,IAAI,CAACzB,GAAN,EAAW+J,OAAO,CAAC/J,GAAnB,CAAjB;EACA+J,IAAAA,OAAO,CAAC7J,KAAR,GAAgBpC,GAAG,CAAC2D,IAAI,CAACvB,KAAN,EAAa6J,OAAO,CAAC7J,KAArB,CAAnB;EACA6J,IAAAA,OAAO,CAAC5J,MAAR,GAAiBrC,GAAG,CAAC2D,IAAI,CAACtB,MAAN,EAAc4J,OAAO,CAAC5J,MAAtB,CAApB;EACA4J,IAAAA,OAAO,CAAClK,IAAR,GAAejC,GAAG,CAAC6D,IAAI,CAAC5B,IAAN,EAAYkK,OAAO,CAAClK,IAApB,CAAlB;EAEA,WAAOkK,OAAP;EACD,GAToB,EASlBX,0BAA0B,CAACpK,OAAD,EAAU6K,mBAAV,EAA+B5B,QAA/B,CATR,CAArB;EAWA6B,EAAAA,YAAY,CAACvK,KAAb,GAAqBuK,YAAY,CAAC5J,KAAb,GAAqB4J,YAAY,CAACjK,IAAvD;EACAiK,EAAAA,YAAY,CAACrK,MAAb,GAAsBqK,YAAY,CAAC3J,MAAb,GAAsB2J,YAAY,CAAC9J,GAAzD;EACA8J,EAAAA,YAAY,CAAClK,CAAb,GAAiBkK,YAAY,CAACjK,IAA9B;EACAiK,EAAAA,YAAY,CAAC/J,CAAb,GAAiB+J,YAAY,CAAC9J,GAA9B;EAEA,SAAO8J,YAAP;EACD;;ECtGc,SAASE,gBAAT,CACblF,SADa,EAEE;EACf,SAAQA,SAAS,CAACmF,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAR;EACD;;ECJc,SAASC,YAAT,CAAsBpF,SAAtB,EAAwD;EACrE,SAAQA,SAAS,CAACmF,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAR;EACD;;ECFc,SAASE,wBAAT,CACbrF,SADa,EAEF;EACX,SAAO,CAAC,KAAD,EAAQ,QAAR,EAAkBnC,OAAlB,CAA0BmC,SAA1B,KAAwC,CAAxC,GAA4C,GAA5C,GAAkD,GAAzD;EACD;;ECKc,SAASsF,cAAT,OASH;EAAA,MARV1F,SAQU,QARVA,SAQU;EAAA,MAPV1F,OAOU,QAPVA,OAOU;EAAA,MANV8F,SAMU,QANVA,SAMU;EACV,MAAMuF,aAAa,GAAGvF,SAAS,GAAGkF,gBAAgB,CAAClF,SAAD,CAAnB,GAAiC,IAAhE;EACA,MAAMwF,SAAS,GAAGxF,SAAS,GAAGoF,YAAY,CAACpF,SAAD,CAAf,GAA6B,IAAxD;EACA,MAAMyF,OAAO,GAAG7F,SAAS,CAAC9E,CAAV,GAAc8E,SAAS,CAACnF,KAAV,GAAkB,CAAhC,GAAoCP,OAAO,CAACO,KAAR,GAAgB,CAApE;EACA,MAAMiL,OAAO,GAAG9F,SAAS,CAAC3E,CAAV,GAAc2E,SAAS,CAACjF,MAAV,GAAmB,CAAjC,GAAqCT,OAAO,CAACS,MAAR,GAAiB,CAAtE;EAEA,MAAIwC,OAAJ;;EACA,UAAQoI,aAAR;EACE,SAAKrK,GAAL;EACEiC,MAAAA,OAAO,GAAG;EACRrC,QAAAA,CAAC,EAAE2K,OADK;EAERxK,QAAAA,CAAC,EAAE2E,SAAS,CAAC3E,CAAV,GAAcf,OAAO,CAACS;EAFjB,OAAV;EAIA;;EACF,SAAKU,MAAL;EACE8B,MAAAA,OAAO,GAAG;EACRrC,QAAAA,CAAC,EAAE2K,OADK;EAERxK,QAAAA,CAAC,EAAE2E,SAAS,CAAC3E,CAAV,GAAc2E,SAAS,CAACjF;EAFnB,OAAV;EAIA;;EACF,SAAKS,KAAL;EACE+B,MAAAA,OAAO,GAAG;EACRrC,QAAAA,CAAC,EAAE8E,SAAS,CAAC9E,CAAV,GAAc8E,SAAS,CAACnF,KADnB;EAERQ,QAAAA,CAAC,EAAEyK;EAFK,OAAV;EAIA;;EACF,SAAK3K,IAAL;EACEoC,MAAAA,OAAO,GAAG;EACRrC,QAAAA,CAAC,EAAE8E,SAAS,CAAC9E,CAAV,GAAcZ,OAAO,CAACO,KADjB;EAERQ,QAAAA,CAAC,EAAEyK;EAFK,OAAV;EAIA;;EACF;EACEvI,MAAAA,OAAO,GAAG;EACRrC,QAAAA,CAAC,EAAE8E,SAAS,CAAC9E,CADL;EAERG,QAAAA,CAAC,EAAE2E,SAAS,CAAC3E;EAFL,OAAV;EA1BJ;;EAgCA,MAAM0K,QAAQ,GAAGJ,aAAa,GAC1BF,wBAAwB,CAACE,aAAD,CADE,GAE1B,IAFJ;;EAIA,MAAII,QAAQ,IAAI,IAAhB,EAAsB;EACpB,QAAMC,GAAG,GAAGD,QAAQ,KAAK,GAAb,GAAmB,QAAnB,GAA8B,OAA1C;;EAEA,YAAQH,SAAR;EACE,WAAKjG,KAAL;EACEpC,QAAAA,OAAO,CAACwI,QAAD,CAAP,GACExI,OAAO,CAACwI,QAAD,CAAP,IAAqB/F,SAAS,CAACgG,GAAD,CAAT,GAAiB,CAAjB,GAAqB1L,OAAO,CAAC0L,GAAD,CAAP,GAAe,CAAzD,CADF;EAEA;;EACF,WAAKpG,GAAL;EACErC,QAAAA,OAAO,CAACwI,QAAD,CAAP,GACExI,OAAO,CAACwI,QAAD,CAAP,IAAqB/F,SAAS,CAACgG,GAAD,CAAT,GAAiB,CAAjB,GAAqB1L,OAAO,CAAC0L,GAAD,CAAP,GAAe,CAAzD,CADF;EAEA;EARJ;EAWD;;EAED,SAAOzI,OAAP;EACD;;EC9Ec,SAAS0I,kBAAT,GAA0C;EACvD,SAAO;EACL3K,IAAAA,GAAG,EAAE,CADA;EAELE,IAAAA,KAAK,EAAE,CAFF;EAGLC,IAAAA,MAAM,EAAE,CAHH;EAILN,IAAAA,IAAI,EAAE;EAJD,GAAP;EAMD;;ECNc,SAAS+K,kBAAT,CACbC,aADa,EAED;EACZ,2BACKF,kBAAkB,EADvB,EAEKE,aAFL;EAID;;ECTc,SAASC,eAAT,CAGbC,KAHa,EAGHjD,IAHG,EAGmC;EAChD,SAAOA,IAAI,CAAClD,MAAL,CAAY,UAACoG,OAAD,EAAUjD,GAAV,EAAkB;EACnCiD,IAAAA,OAAO,CAACjD,GAAD,CAAP,GAAegD,KAAf;EACA,WAAOC,OAAP;EACD,GAHM,EAGJ,EAHI,CAAP;EAID;;ECuBc,SAASC,cAAT,CACbC,KADa,EAEbvD,OAFa,EAGD;EAAA,MADZA,OACY;EADZA,IAAAA,OACY,GADe,EACf;EAAA;;EAAA,iBASRA,OATQ;EAAA,oCAEV7C,SAFU;EAAA,MAEVA,SAFU,mCAEEoG,KAAK,CAACpG,SAFR;EAAA,mCAGVmD,QAHU;EAAA,MAGVA,QAHU,kCAGCiD,KAAK,CAACjD,QAHP;EAAA,mCAIVyB,QAJU;EAAA,MAIVA,QAJU,kCAICnF,eAJD;EAAA,uCAKVoF,YALU;EAAA,MAKVA,YALU,sCAKKnF,QALL;EAAA,uCAMV2G,cANU;EAAA,MAMVA,cANU,sCAMO1G,MANP;EAAA,sCAOV2G,WAPU;EAAA,MAOVA,WAPU,qCAOI,KAPJ;EAAA,kCAQVC,OARU;EAAA,MAQVA,OARU,iCAQA,CARA;EAWZ,MAAMR,aAAa,GAAGD,kBAAkB,CACtC,OAAOS,OAAP,KAAmB,QAAnB,GACIA,OADJ,GAEIP,eAAe,CAACO,OAAD,EAAUjH,cAAV,CAHmB,CAAxC;EAMA,MAAMkH,UAAU,GAAGH,cAAc,KAAK1G,MAAnB,GAA4BC,SAA5B,GAAwCD,MAA3D;EAEA,MAAM8G,UAAU,GAAGL,KAAK,CAACM,KAAN,CAAY/G,MAA/B;EACA,MAAMzF,OAAO,GAAGkM,KAAK,CAACO,QAAN,CAAeL,WAAW,GAAGE,UAAH,GAAgBH,cAA1C,CAAhB;EAEA,MAAMO,kBAAkB,GAAGjC,eAAe,CACxCpM,SAAS,CAAC2B,OAAD,CAAT,GACIA,OADJ,GAEIA,OAAO,CAAC2M,cAAR,IAA0B5K,kBAAkB,CAACmK,KAAK,CAACO,QAAN,CAAehH,MAAhB,CAHR,EAIxCiF,QAJwC,EAKxCC,YALwC,EAMxC1B,QANwC,CAA1C;EASA,MAAM2D,mBAAmB,GAAG7M,qBAAqB,CAACmM,KAAK,CAACO,QAAN,CAAe/G,SAAhB,CAAjD;EAEA,MAAMmH,aAAa,GAAGzB,cAAc,CAAC;EACnC1F,IAAAA,SAAS,EAAEkH,mBADwB;EAEnC5M,IAAAA,OAAO,EAAEuM,UAF0B;EAGnCtD,IAAAA,QAAQ,EAAE,UAHyB;EAInCnD,IAAAA,SAAS,EAATA;EAJmC,GAAD,CAApC;EAOA,MAAMgH,gBAAgB,GAAG5C,gBAAgB,mBACpCqC,UADoC,EAEpCM,aAFoC,EAAzC;EAKA,MAAME,iBAAiB,GACrBZ,cAAc,KAAK1G,MAAnB,GAA4BqH,gBAA5B,GAA+CF,mBADjD,CA7CY;EAiDZ;;EACA,MAAMI,eAAe,GAAG;EACtBhM,IAAAA,GAAG,EAAE0L,kBAAkB,CAAC1L,GAAnB,GAAyB+L,iBAAiB,CAAC/L,GAA3C,GAAiD6K,aAAa,CAAC7K,GAD9C;EAEtBG,IAAAA,MAAM,EACJ4L,iBAAiB,CAAC5L,MAAlB,GACAuL,kBAAkB,CAACvL,MADnB,GAEA0K,aAAa,CAAC1K,MALM;EAMtBN,IAAAA,IAAI,EAAE6L,kBAAkB,CAAC7L,IAAnB,GAA0BkM,iBAAiB,CAAClM,IAA5C,GAAmDgL,aAAa,CAAChL,IANjD;EAOtBK,IAAAA,KAAK,EACH6L,iBAAiB,CAAC7L,KAAlB,GAA0BwL,kBAAkB,CAACxL,KAA7C,GAAqD2K,aAAa,CAAC3K;EAR/C,GAAxB;EAWA,MAAM+L,UAAU,GAAGf,KAAK,CAACgB,aAAN,CAAoBC,MAAvC,CA7DY;;EAgEZ,MAAIhB,cAAc,KAAK1G,MAAnB,IAA6BwH,UAAjC,EAA6C;EAC3C,QAAME,MAAM,GAAGF,UAAU,CAACnH,SAAD,CAAzB;EAEA+C,IAAAA,MAAM,CAACC,IAAP,CAAYkE,eAAZ,EAA6BhG,OAA7B,CAAqC,UAAC+B,GAAD,EAAS;EAC5C,UAAMqE,QAAQ,GAAG,CAAClM,KAAD,EAAQC,MAAR,EAAgBwC,OAAhB,CAAwBoF,GAAxB,KAAgC,CAAhC,GAAoC,CAApC,GAAwC,CAAC,CAA1D;EACA,UAAMsE,IAAI,GAAG,CAACrM,GAAD,EAAMG,MAAN,EAAcwC,OAAd,CAAsBoF,GAAtB,KAA8B,CAA9B,GAAkC,GAAlC,GAAwC,GAArD;EACAiE,MAAAA,eAAe,CAACjE,GAAD,CAAf,IAAwBoE,MAAM,CAACE,IAAD,CAAN,GAAeD,QAAvC;EACD,KAJD;EAKD;;EAED,SAAOJ,eAAP;EACD;;EC7FD,IAAMM,eAAoC,GAAG;EAC3CxH,EAAAA,SAAS,EAAE,QADgC;EAE3Ca,EAAAA,SAAS,EAAE,EAFgC;EAG3CsC,EAAAA,QAAQ,EAAE;EAHiC,CAA7C;;EAWA,SAASsE,gBAAT,GAAwD;EAAA,oCAA3BC,IAA2B;EAA3BA,IAAAA,IAA2B;EAAA;;EACtD,SAAO,CAACA,IAAI,CAACC,IAAL,CACN,UAACzN,OAAD;EAAA,WACE,EAAEA,OAAO,IAAI,OAAOA,OAAO,CAACD,qBAAf,KAAyC,UAAtD,CADF;EAAA,GADM,CAAR;EAID;;EAEM,SAAS2N,eAAT,CAAyBC,gBAAzB,EAAqE;EAAA,MAA5CA,gBAA4C;EAA5CA,IAAAA,gBAA4C,GAAJ,EAAI;EAAA;;EAAA,0BAExEA,gBAFwE;EAAA,gDAClEC,gBADkE;EAAA,MAClEA,gBADkE,sCAC/C,EAD+C;EAAA,iDAC3CC,cAD2C;EAAA,MAC3CA,cAD2C,uCAC1BP,eAD0B;EAI1E,SAAO,SAASQ,YAAT,CACLpI,SADK,EAELD,MAFK,EAGLkD,OAHK,EAIK;EAAA,QADVA,OACU;EADVA,MAAAA,OACU,GADmCkF,cACnC;EAAA;;EACV,QAAI3B,KAAoB,GAAG;EACzBpG,MAAAA,SAAS,EAAE,QADc;EAEzBgC,MAAAA,gBAAgB,EAAE,EAFO;EAGzBa,MAAAA,OAAO,oBAAO2E,eAAP,EAA2BO,cAA3B,CAHkB;EAIzBX,MAAAA,aAAa,EAAE,EAJU;EAKzBT,MAAAA,QAAQ,EAAE;EACR/G,QAAAA,SAAS,EAATA,SADQ;EAERD,QAAAA,MAAM,EAANA;EAFQ,OALe;EASzBsI,MAAAA,UAAU,EAAE,EATa;EAUzBC,MAAAA,MAAM,EAAE;EAViB,KAA3B;EAaA,QAAIC,gBAAmC,GAAG,EAA1C;EACA,QAAIC,WAAW,GAAG,KAAlB;EAEA,QAAMC,QAAQ,GAAG;EACfjC,MAAAA,KAAK,EAALA,KADe;EAEfkC,MAAAA,UAFe,sBAEJC,gBAFI,EAEc;EAC3B,YAAM1F,OAAO,GACX,OAAO0F,gBAAP,KAA4B,UAA5B,GACIA,gBAAgB,CAACnC,KAAK,CAACvD,OAAP,CADpB,GAEI0F,gBAHN;EAKAC,QAAAA,sBAAsB;EAEtBpC,QAAAA,KAAK,CAACvD,OAAN,qBAEKkF,cAFL,EAGK3B,KAAK,CAACvD,OAHX,EAIKA,OAJL;EAOAuD,QAAAA,KAAK,CAACqC,aAAN,GAAsB;EACpB7I,UAAAA,SAAS,EAAErH,SAAS,CAACqH,SAAD,CAAT,GACP7B,iBAAiB,CAAC6B,SAAD,CADV,GAEPA,SAAS,CAACiH,cAAV,GACA9I,iBAAiB,CAAC6B,SAAS,CAACiH,cAAX,CADjB,GAEA,EALgB;EAMpBlH,UAAAA,MAAM,EAAE5B,iBAAiB,CAAC4B,MAAD;EANL,SAAtB,CAf2B;EAyB3B;;EACA,YAAMqC,gBAAgB,GAAGD,cAAc,CACrCU,WAAW,WAAKqF,gBAAL,EAA0B1B,KAAK,CAACvD,OAAN,CAAchC,SAAxC,EAD0B,CAAvC,CA1B2B;;EA+B3BuF,QAAAA,KAAK,CAACpE,gBAAN,GAAyBA,gBAAgB,CAAC7C,MAAjB,CAAwB,UAACuJ,CAAD;EAAA,iBAAOA,CAAC,CAACC,OAAT;EAAA,SAAxB,CAAzB;EAEAC,QAAAA,kBAAkB;EAElB,eAAOP,QAAQ,CAACQ,MAAT,EAAP;EACD,OAtCc;EAwCf;EACA;EACA;EACA;EACA;EACAC,MAAAA,WA7Ce,yBA6CD;EACZ,YAAIV,WAAJ,EAAiB;EACf;EACD;;EAHW,8BAKkBhC,KAAK,CAACO,QALxB;EAAA,YAKJ/G,SALI,mBAKJA,SALI;EAAA,YAKOD,MALP,mBAKOA,MALP;EAQZ;;EACA,YAAI,CAAC8H,gBAAgB,CAAC7H,SAAD,EAAYD,MAAZ,CAArB,EAA0C;EACxC;EACD,SAXW;;;EAcZyG,QAAAA,KAAK,CAACM,KAAN,GAAc;EACZ9G,UAAAA,SAAS,EAAEhD,gBAAgB,CACzBgD,SADyB,EAEzBR,eAAe,CAACO,MAAD,CAFU,EAGzByG,KAAK,CAACvD,OAAN,CAAcM,QAAd,KAA2B,OAHF,CADf;EAMZxD,UAAAA,MAAM,EAAErC,aAAa,CAACqC,MAAD;EANT,SAAd,CAdY;EAwBZ;EACA;EACA;EACA;;EACAyG,QAAAA,KAAK,CAAC2C,KAAN,GAAc,KAAd;EAEA3C,QAAAA,KAAK,CAACpG,SAAN,GAAkBoG,KAAK,CAACvD,OAAN,CAAc7C,SAAhC,CA9BY;EAiCZ;EACA;EACA;;EACAoG,QAAAA,KAAK,CAACpE,gBAAN,CAAuBd,OAAvB,CACE,UAACC,QAAD;EAAA,iBACGiF,KAAK,CAACgB,aAAN,CAAoBjG,QAAQ,CAACE,IAA7B,sBACIF,QAAQ,CAAC2B,IADb,CADH;EAAA,SADF;;EAOA,aAAK,IAAIkG,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG5C,KAAK,CAACpE,gBAAN,CAAuBiH,MAAnD,EAA2DD,KAAK,EAAhE,EAAoE;EAClE,cAAI5C,KAAK,CAAC2C,KAAN,KAAgB,IAApB,EAA0B;EACxB3C,YAAAA,KAAK,CAAC2C,KAAN,GAAc,KAAd;EACAC,YAAAA,KAAK,GAAG,CAAC,CAAT;EACA;EACD;;EALiE,sCAO/B5C,KAAK,CAACpE,gBAAN,CAAuBgH,KAAvB,CAP+B;EAAA,cAO1D7G,EAP0D,yBAO1DA,EAP0D;EAAA,6DAOtDU,OAPsD;EAAA,cAOtDA,QAPsD,uCAO5C,EAP4C;EAAA,cAOxCxB,IAPwC,yBAOxCA,IAPwC;;EASlE,cAAI,OAAOc,EAAP,KAAc,UAAlB,EAA8B;EAC5BiE,YAAAA,KAAK,GAAGjE,EAAE,CAAC;EAAEiE,cAAAA,KAAK,EAALA,KAAF;EAASvD,cAAAA,OAAO,EAAPA,QAAT;EAAkBxB,cAAAA,IAAI,EAAJA,IAAlB;EAAwBgH,cAAAA,QAAQ,EAARA;EAAxB,aAAD,CAAF,IAA0CjC,KAAlD;EACD;EACF;EACF,OArGc;EAuGf;EACA;EACAyC,MAAAA,MAAM,EAAE3G,QAAQ,CACd;EAAA,eACE,IAAIG,OAAJ,CAA2B,UAACC,OAAD,EAAa;EACtC+F,UAAAA,QAAQ,CAACS,WAAT;EACAxG,UAAAA,OAAO,CAAC8D,KAAD,CAAP;EACD,SAHD,CADF;EAAA,OADc,CAzGD;EAiHf8C,MAAAA,OAjHe,qBAiHL;EACRV,QAAAA,sBAAsB;EACtBJ,QAAAA,WAAW,GAAG,IAAd;EACD;EApHc,KAAjB;;EAuHA,QAAI,CAACX,gBAAgB,CAAC7H,SAAD,EAAYD,MAAZ,CAArB,EAA0C;EACxC,aAAO0I,QAAP;EACD;;EAEDA,IAAAA,QAAQ,CAACC,UAAT,CAAoBzF,OAApB,EAA6BN,IAA7B,CAAkC,UAAC6D,KAAD,EAAW;EAC3C,UAAI,CAACgC,WAAD,IAAgBvF,OAAO,CAACsG,aAA5B,EAA2C;EACzCtG,QAAAA,OAAO,CAACsG,aAAR,CAAsB/C,KAAtB;EACD;EACF,KAJD,EA5IU;EAmJV;EACA;EACA;EACA;;EACA,aAASwC,kBAAT,GAA8B;EAC5BxC,MAAAA,KAAK,CAACpE,gBAAN,CAAuBd,OAAvB,CAA+B,gBAAoC;EAAA,YAAjCG,IAAiC,QAAjCA,IAAiC;EAAA,gCAA3BwB,OAA2B;EAAA,YAA3BA,OAA2B,6BAAjB,EAAiB;EAAA,YAAbuG,MAAa,QAAbA,MAAa;;EACjE,YAAI,OAAOA,MAAP,KAAkB,UAAtB,EAAkC;EAChC,cAAMC,SAAS,GAAGD,MAAM,CAAC;EAAEhD,YAAAA,KAAK,EAALA,KAAF;EAAS/E,YAAAA,IAAI,EAAJA,IAAT;EAAegH,YAAAA,QAAQ,EAARA,QAAf;EAAyBxF,YAAAA,OAAO,EAAPA;EAAzB,WAAD,CAAxB;;EACA,cAAMyG,MAAM,GAAG,SAATA,MAAS,GAAM,EAArB;;EACAnB,UAAAA,gBAAgB,CAACrG,IAAjB,CAAsBuH,SAAS,IAAIC,MAAnC;EACD;EACF,OAND;EAOD;;EAED,aAASd,sBAAT,GAAkC;EAChCL,MAAAA,gBAAgB,CAACjH,OAAjB,CAAyB,UAACiB,EAAD;EAAA,eAAQA,EAAE,EAAV;EAAA,OAAzB;EACAgG,MAAAA,gBAAgB,GAAG,EAAnB;EACD;;EAED,WAAOE,QAAP;EACD,GA3KD;EA4KD;;EC1MD,IAAMkB,OAAO,GAAG;EAAEA,EAAAA,OAAO,EAAE;EAAX,CAAhB;;EAEA,SAASH,QAAT,OAA0E;EAAA,MAAxDhD,KAAwD,QAAxDA,KAAwD;EAAA,MAAjDiC,QAAiD,QAAjDA,QAAiD;EAAA,MAAvCxF,OAAuC,QAAvCA,OAAuC;EAAA,wBAC/BA,OAD+B,CAChE3F,MADgE;EAAA,MAChEA,MADgE,gCACvD,IADuD;EAAA,wBAC/B2F,OAD+B,CACjD2G,MADiD;EAAA,MACjDA,MADiD,gCACxC,IADwC;EAGxE,MAAMrR,MAAM,GAAGF,SAAS,CAACmO,KAAK,CAACO,QAAN,CAAehH,MAAhB,CAAxB;EACA,MAAM8I,aAAa,aACdrC,KAAK,CAACqC,aAAN,CAAoB7I,SADN,EAEdwG,KAAK,CAACqC,aAAN,CAAoB9I,MAFN,CAAnB;;EAKA,MAAIzC,MAAJ,EAAY;EACVuL,IAAAA,aAAa,CAACvH,OAAd,CAAsB,UAAAjD,YAAY,EAAI;EACpCA,MAAAA,YAAY,CAACwL,gBAAb,CAA8B,QAA9B,EAAwCpB,QAAQ,CAACQ,MAAjD,EAAyDU,OAAzD;EACD,KAFD;EAGD;;EAED,MAAIC,MAAJ,EAAY;EACVrR,IAAAA,MAAM,CAACsR,gBAAP,CAAwB,QAAxB,EAAkCpB,QAAQ,CAACQ,MAA3C,EAAmDU,OAAnD;EACD;;EAED,SAAO,YAAM;EACX,QAAIrM,MAAJ,EAAY;EACVuL,MAAAA,aAAa,CAACvH,OAAd,CAAsB,UAAAjD,YAAY,EAAI;EACpCA,QAAAA,YAAY,CAACyL,mBAAb,CAAiC,QAAjC,EAA2CrB,QAAQ,CAACQ,MAApD,EAA4DU,OAA5D;EACD,OAFD;EAGD;;EAED,QAAIC,MAAJ,EAAY;EACVrR,MAAAA,MAAM,CAACuR,mBAAP,CAA2B,QAA3B,EAAqCrB,QAAQ,CAACQ,MAA9C,EAAsDU,OAAtD;EACD;EACF,GAVD;EAWD;;;AAID,uBAAgB;EACdlI,EAAAA,IAAI,EAAE,gBADQ;EAEdsH,EAAAA,OAAO,EAAE,IAFK;EAGd1G,EAAAA,KAAK,EAAE,OAHO;EAIdE,EAAAA,EAAE,EAAE,cAAM,EAJI;EAKdiH,EAAAA,MAAM,EAANA,QALc;EAMdtG,EAAAA,IAAI,EAAE;EANQ,CAAhB;;EC1CA,SAASiE,aAAT,OAAiE;EAAA,MAAxCX,KAAwC,QAAxCA,KAAwC;EAAA,MAAjC/E,IAAiC,QAAjCA,IAAiC;EAC/D;EACA;EACA;EACA;EACA+E,EAAAA,KAAK,CAACgB,aAAN,CAAoB/F,IAApB,IAA4BiE,cAAc,CAAC;EACzC1F,IAAAA,SAAS,EAAEwG,KAAK,CAACM,KAAN,CAAY9G,SADkB;EAEzC1F,IAAAA,OAAO,EAAEkM,KAAK,CAACM,KAAN,CAAY/G,MAFoB;EAGzCwD,IAAAA,QAAQ,EAAE,UAH+B;EAIzCnD,IAAAA,SAAS,EAAEoG,KAAK,CAACpG;EAJwB,GAAD,CAA1C;EAMD;;;AAID,wBAAgB;EACdqB,EAAAA,IAAI,EAAE,eADQ;EAEdsH,EAAAA,OAAO,EAAE,IAFK;EAGd1G,EAAAA,KAAK,EAAE,MAHO;EAIdE,EAAAA,EAAE,EAAE4E,aAJU;EAKdjE,EAAAA,IAAI,EAAE;EALQ,CAAhB;;ECmBA,IAAM6G,UAAU,GAAG;EACjBzO,EAAAA,GAAG,EAAE,MADY;EAEjBE,EAAAA,KAAK,EAAE,MAFU;EAGjBC,EAAAA,MAAM,EAAE,MAHS;EAIjBN,EAAAA,IAAI,EAAE;EAJW,CAAnB;EAQA;EACA;;EACA,SAAS6O,iBAAT,OAAqCrO,GAArC,EAA2D;EAAA,MAA9BT,CAA8B,QAA9BA,CAA8B;EAAA,MAA3BG,CAA2B,QAA3BA,CAA2B;EACzD,MAAM4O,GAAG,GAAGtO,GAAG,CAACuO,gBAAJ,IAAwB,CAApC;EAEA,SAAO;EACLhP,IAAAA,CAAC,EAAE7B,KAAK,CAAC6B,CAAC,GAAG+O,GAAL,CAAL,GAAiBA,GAAjB,IAAwB,CADtB;EAEL5O,IAAAA,CAAC,EAAEhC,KAAK,CAACgC,CAAC,GAAG4O,GAAL,CAAL,GAAiBA,GAAjB,IAAwB;EAFtB,GAAP;EAID;;EAEM,SAASE,WAAT,QAsBJ;EAAA;;EAAA,MArBDpK,MAqBC,SArBDA,MAqBC;EAAA,MApBD8G,UAoBC,SApBDA,UAoBC;EAAA,MAnBDzG,SAmBC,SAnBDA,SAmBC;EAAA,MAlBDwF,SAkBC,SAlBDA,SAkBC;EAAA,MAjBDrI,OAiBC,SAjBDA,OAiBC;EAAA,MAhBDqB,QAgBC,SAhBDA,QAgBC;EAAA,MAfDwL,eAeC,SAfDA,eAeC;EAAA,MAdDC,QAcC,SAdDA,QAcC;EAAA,MAbDC,YAaC,SAbDA,YAaC;EAAA,MAZDnN,OAYC,SAZDA,OAYC;EAAA,mBACsBI,OADtB,CACKrC,CADL;EAAA,MACKA,CADL,2BACS,CADT;EAAA,mBACsBqC,OADtB,CACYlC,CADZ;EAAA,MACYA,CADZ,2BACgB,CADhB;;EAAA,cAIC,OAAOiP,YAAP,KAAwB,UAAxB,GAAqCA,YAAY,CAAC;EAAEpP,IAAAA,CAAC,EAADA,CAAF;EAAKG,IAAAA,CAAC,EAADA;EAAL,GAAD,CAAjD,GAA8D;EAAEH,IAAAA,CAAC,EAADA,CAAF;EAAKG,IAAAA,CAAC,EAADA;EAAL,GAJ/D;;EAGEH,EAAAA,CAHF,SAGEA,CAHF;EAGKG,EAAAA,CAHL,SAGKA,CAHL;EAMD,MAAMkP,IAAI,GAAGhN,OAAO,CAACiN,cAAR,CAAuB,GAAvB,CAAb;EACA,MAAMC,IAAI,GAAGlN,OAAO,CAACiN,cAAR,CAAuB,GAAvB,CAAb;EAEA,MAAIE,KAAa,GAAGvP,IAApB;EACA,MAAIwP,KAAa,GAAGrP,GAApB;EAEA,MAAMK,GAAW,GAAGpD,MAApB;;EAEA,MAAI8R,QAAJ,EAAc;EACZ,QAAInN,YAAY,GAAGsC,eAAe,CAACO,MAAD,CAAlC;EACA,QAAI6K,UAAU,GAAG,cAAjB;EACA,QAAIC,SAAS,GAAG,aAAhB;;EAEA,QAAI3N,YAAY,KAAK7E,SAAS,CAAC0H,MAAD,CAA9B,EAAwC;EACtC7C,MAAAA,YAAY,GAAGb,kBAAkB,CAAC0D,MAAD,CAAjC;;EAEA,UACEtD,gBAAgB,CAACS,YAAD,CAAhB,CAA+B0B,QAA/B,KAA4C,QAA5C,IACAA,QAAQ,KAAK,UAFf,EAGE;EACAgM,QAAAA,UAAU,GAAG,cAAb;EACAC,QAAAA,SAAS,GAAG,aAAZ;EACD;EACF,KAfW;;;EAkBZ3N,IAAAA,YAAY,GAAIA,YAAhB;;EAEA,QACEkD,SAAS,KAAK9E,GAAd,IACC,CAAC8E,SAAS,KAAKjF,IAAd,IAAsBiF,SAAS,KAAK5E,KAArC,KAA+CoK,SAAS,KAAKhG,GAFhE,EAGE;EACA+K,MAAAA,KAAK,GAAGlP,MAAR;EACA,UAAMqP,OAAO,GACX3N,OAAO,IAAID,YAAY,KAAKvB,GAA5B,IAAmCA,GAAG,CAACX,cAAvC,GACIW,GAAG,CAACX,cAAJ,CAAmBD,MADvB;EAGImC,MAAAA,YAAY,CAAC0N,UAAD,CAJlB;EAKAvP,MAAAA,CAAC,IAAIyP,OAAO,GAAGjE,UAAU,CAAC9L,MAA1B;EACAM,MAAAA,CAAC,IAAI+O,eAAe,GAAG,CAAH,GAAO,CAAC,CAA5B;EACD;;EAED,QACEhK,SAAS,KAAKjF,IAAd,IACC,CAACiF,SAAS,KAAK9E,GAAd,IAAqB8E,SAAS,KAAK3E,MAApC,KAA+CmK,SAAS,KAAKhG,GAFhE,EAGE;EACA8K,MAAAA,KAAK,GAAGlP,KAAR;EACA,UAAMuP,OAAO,GACX5N,OAAO,IAAID,YAAY,KAAKvB,GAA5B,IAAmCA,GAAG,CAACX,cAAvC,GACIW,GAAG,CAACX,cAAJ,CAAmBH,KADvB;EAGIqC,MAAAA,YAAY,CAAC2N,SAAD,CAJlB;EAKA3P,MAAAA,CAAC,IAAI6P,OAAO,GAAGlE,UAAU,CAAChM,KAA1B;EACAK,MAAAA,CAAC,IAAIkP,eAAe,GAAG,CAAH,GAAO,CAAC,CAA5B;EACD;EACF;;EAED,MAAMY,YAAY;EAChBpM,IAAAA,QAAQ,EAARA;EADgB,KAEZyL,QAAQ,IAAIN,UAFA,CAAlB;;EA/DC,cAqECO,YAAY,KAAK,IAAjB,GACIN,iBAAiB,CAAC;EAAE9O,IAAAA,CAAC,EAADA,CAAF;EAAKG,IAAAA,CAAC,EAADA;EAAL,GAAD,EAAWhD,SAAS,CAAC0H,MAAD,CAApB,CADrB,GAEI;EAAE7E,IAAAA,CAAC,EAADA,CAAF;EAAKG,IAAAA,CAAC,EAADA;EAAL,GAvEL;;EAoEEH,EAAAA,CApEF,SAoEEA,CApEF;EAoEKG,EAAAA,CApEL,SAoEKA,CApEL;;EAyED,MAAI+O,eAAJ,EAAqB;EAAA;;EACnB,6BACKY,YADL,uCAEGL,KAFH,IAEWF,IAAI,GAAG,GAAH,GAAS,EAFxB,iBAGGC,KAHH,IAGWH,IAAI,GAAG,GAAH,GAAS,EAHxB,iBAOEpL,SAPF,GAQI,CAACxD,GAAG,CAACuO,gBAAJ,IAAwB,CAAzB,KAA+B,CAA/B,kBACiBhP,CADjB,YACyBG,CADzB,4BAEmBH,CAFnB,YAE2BG,CAF3B,WARJ;EAYD;;EAED,2BACK2P,YADL,yCAEGL,KAFH,IAEWF,IAAI,GAAMpP,CAAN,UAAc,EAF7B,kBAGGqP,KAHH,IAGWH,IAAI,GAAMrP,CAAN,UAAc,EAH7B,kBAIEiE,SAJF,GAIa,EAJb;EAMD;;EAED,SAAS8L,aAAT,QAAuE;EAAA,MAA9CzE,KAA8C,SAA9CA,KAA8C;EAAA,MAAvCvD,OAAuC,SAAvCA,OAAuC;EAAA,8BAMjEA,OANiE,CAEnEmH,eAFmE;EAAA,MAEnEA,eAFmE,sCAEjD,IAFiD;EAAA,0BAMjEnH,OANiE,CAGnEoH,QAHmE;EAAA,MAGnEA,QAHmE,kCAGxD,IAHwD;EAAA,8BAMjEpH,OANiE,CAKnEqH,YALmE;EAAA,MAKnEA,YALmE,sCAKpD,IALoD;EAQrE,MAAMU,YAAY,GAAG;EACnB5K,IAAAA,SAAS,EAAEkF,gBAAgB,CAACkB,KAAK,CAACpG,SAAP,CADR;EAEnBwF,IAAAA,SAAS,EAAEJ,YAAY,CAACgB,KAAK,CAACpG,SAAP,CAFJ;EAGnBL,IAAAA,MAAM,EAAEyG,KAAK,CAACO,QAAN,CAAehH,MAHJ;EAInB8G,IAAAA,UAAU,EAAEL,KAAK,CAACM,KAAN,CAAY/G,MAJL;EAKnBqK,IAAAA,eAAe,EAAfA,eALmB;EAMnBjN,IAAAA,OAAO,EAAEqJ,KAAK,CAACvD,OAAN,CAAcM,QAAd,KAA2B;EANjB,GAArB;;EASA,MAAIiD,KAAK,CAACgB,aAAN,CAAoBL,aAApB,IAAqC,IAAzC,EAA+C;EAC7CX,IAAAA,KAAK,CAAC8B,MAAN,CAAavI,MAAb,qBACKyG,KAAK,CAAC8B,MAAN,CAAavI,MADlB,EAEKoK,WAAW,mBACTa,YADS;EAEZzN,MAAAA,OAAO,EAAEiJ,KAAK,CAACgB,aAAN,CAAoBL,aAFjB;EAGZvI,MAAAA,QAAQ,EAAE4H,KAAK,CAACvD,OAAN,CAAcM,QAHZ;EAIZ8G,MAAAA,QAAQ,EAARA,QAJY;EAKZC,MAAAA,YAAY,EAAZA;EALY,OAFhB;EAUD;;EAED,MAAI9D,KAAK,CAACgB,aAAN,CAAoB0D,KAApB,IAA6B,IAAjC,EAAuC;EACrC1E,IAAAA,KAAK,CAAC8B,MAAN,CAAa4C,KAAb,qBACK1E,KAAK,CAAC8B,MAAN,CAAa4C,KADlB,EAEKf,WAAW,mBACTa,YADS;EAEZzN,MAAAA,OAAO,EAAEiJ,KAAK,CAACgB,aAAN,CAAoB0D,KAFjB;EAGZtM,MAAAA,QAAQ,EAAE,UAHE;EAIZyL,MAAAA,QAAQ,EAAE,KAJE;EAKZC,MAAAA,YAAY,EAAZA;EALY,OAFhB;EAUD;;EAED9D,EAAAA,KAAK,CAAC6B,UAAN,CAAiBtI,MAAjB,qBACKyG,KAAK,CAAC6B,UAAN,CAAiBtI,MADtB;EAEE,6BAAyByG,KAAK,CAACpG;EAFjC;EAID;;;AAID,wBAAgB;EACdqB,EAAAA,IAAI,EAAE,eADQ;EAEdsH,EAAAA,OAAO,EAAE,IAFK;EAGd1G,EAAAA,KAAK,EAAE,aAHO;EAIdE,EAAAA,EAAE,EAAE0I,aAJU;EAKd/H,EAAAA,IAAI,EAAE;EALQ,CAAhB;;EC5NA;;EAEA,SAASiI,WAAT,OAAyD;EAAA,MAAlC3E,KAAkC,QAAlCA,KAAkC;EACvDrD,EAAAA,MAAM,CAACC,IAAP,CAAYoD,KAAK,CAACO,QAAlB,EAA4BzF,OAA5B,CAAoC,UAACG,IAAD,EAAU;EAC5C,QAAM2J,KAAK,GAAG5E,KAAK,CAAC8B,MAAN,CAAa7G,IAAb,KAAsB,EAApC;EAEA,QAAM4G,UAAU,GAAG7B,KAAK,CAAC6B,UAAN,CAAiB5G,IAAjB,KAA0B,EAA7C;EACA,QAAMnH,OAAO,GAAGkM,KAAK,CAACO,QAAN,CAAetF,IAAf,CAAhB,CAJ4C;;EAO5C,QAAI,CAAC3I,aAAa,CAACwB,OAAD,CAAd,IAA2B,CAAC4B,WAAW,CAAC5B,OAAD,CAA3C,EAAsD;EACpD;EACD,KAT2C;EAY5C;EACA;;;EACA6I,IAAAA,MAAM,CAACkI,MAAP,CAAc/Q,OAAO,CAAC8Q,KAAtB,EAA6BA,KAA7B;EAEAjI,IAAAA,MAAM,CAACC,IAAP,CAAYiF,UAAZ,EAAwB/G,OAAxB,CAAgC,UAACG,IAAD,EAAU;EACxC,UAAM4E,KAAK,GAAGgC,UAAU,CAAC5G,IAAD,CAAxB;;EACA,UAAI4E,KAAK,KAAK,KAAd,EAAqB;EACnB/L,QAAAA,OAAO,CAACgR,eAAR,CAAwB7J,IAAxB;EACD,OAFD,MAEO;EACLnH,QAAAA,OAAO,CAACiR,YAAR,CAAqB9J,IAArB,EAA2B4E,KAAK,KAAK,IAAV,GAAiB,EAAjB,GAAsBA,KAAjD;EACD;EACF,KAPD;EAQD,GAxBD;EAyBD;;EAED,SAASmD,QAAT,QAAoD;EAAA,MAAlChD,KAAkC,SAAlCA,KAAkC;EAClD,MAAMgF,aAAa,GAAG;EACpBzL,IAAAA,MAAM,EAAE;EACNnB,MAAAA,QAAQ,EAAE4H,KAAK,CAACvD,OAAN,CAAcM,QADlB;EAENpI,MAAAA,IAAI,EAAE,GAFA;EAGNG,MAAAA,GAAG,EAAE,GAHC;EAINmQ,MAAAA,MAAM,EAAE;EAJF,KADY;EAOpBP,IAAAA,KAAK,EAAE;EACLtM,MAAAA,QAAQ,EAAE;EADL,KAPa;EAUpBoB,IAAAA,SAAS,EAAE;EAVS,GAAtB;EAaAmD,EAAAA,MAAM,CAACkI,MAAP,CAAc7E,KAAK,CAACO,QAAN,CAAehH,MAAf,CAAsBqL,KAApC,EAA2CI,aAAa,CAACzL,MAAzD;EACAyG,EAAAA,KAAK,CAAC8B,MAAN,GAAekD,aAAf;;EAEA,MAAIhF,KAAK,CAACO,QAAN,CAAemE,KAAnB,EAA0B;EACxB/H,IAAAA,MAAM,CAACkI,MAAP,CAAc7E,KAAK,CAACO,QAAN,CAAemE,KAAf,CAAqBE,KAAnC,EAA0CI,aAAa,CAACN,KAAxD;EACD;;EAED,SAAO,YAAM;EACX/H,IAAAA,MAAM,CAACC,IAAP,CAAYoD,KAAK,CAACO,QAAlB,EAA4BzF,OAA5B,CAAoC,UAACG,IAAD,EAAU;EAC5C,UAAMnH,OAAO,GAAGkM,KAAK,CAACO,QAAN,CAAetF,IAAf,CAAhB;EACA,UAAM4G,UAAU,GAAG7B,KAAK,CAAC6B,UAAN,CAAiB5G,IAAjB,KAA0B,EAA7C;EAEA,UAAMiK,eAAe,GAAGvI,MAAM,CAACC,IAAP,CACtBoD,KAAK,CAAC8B,MAAN,CAAakC,cAAb,CAA4B/I,IAA5B,IACI+E,KAAK,CAAC8B,MAAN,CAAa7G,IAAb,CADJ,GAEI+J,aAAa,CAAC/J,IAAD,CAHK,CAAxB,CAJ4C;;EAW5C,UAAM2J,KAAK,GAAGM,eAAe,CAACxL,MAAhB,CAAuB,UAACkL,KAAD,EAAQO,QAAR,EAAqB;EACxDP,QAAAA,KAAK,CAACO,QAAD,CAAL,GAAkB,EAAlB;EACA,eAAOP,KAAP;EACD,OAHa,EAGX,EAHW,CAAd,CAX4C;;EAiB5C,UAAI,CAACtS,aAAa,CAACwB,OAAD,CAAd,IAA2B,CAAC4B,WAAW,CAAC5B,OAAD,CAA3C,EAAsD;EACpD;EACD;;EAED6I,MAAAA,MAAM,CAACkI,MAAP,CAAc/Q,OAAO,CAAC8Q,KAAtB,EAA6BA,KAA7B;EAEAjI,MAAAA,MAAM,CAACC,IAAP,CAAYiF,UAAZ,EAAwB/G,OAAxB,CAAgC,UAACsK,SAAD,EAAe;EAC7CtR,QAAAA,OAAO,CAACgR,eAAR,CAAwBM,SAAxB;EACD,OAFD;EAGD,KA1BD;EA2BD,GA5BD;EA6BD;;;AAID,sBAAgB;EACdnK,EAAAA,IAAI,EAAE,aADQ;EAEdsH,EAAAA,OAAO,EAAE,IAFK;EAGd1G,EAAAA,KAAK,EAAE,OAHO;EAIdE,EAAAA,EAAE,EAAE4I,WAJU;EAKd3B,EAAAA,MAAM,EAANA,QALc;EAMd5H,EAAAA,QAAQ,EAAE,CAAC,eAAD;EANI,CAAhB;;ECtEO,SAASiK,uBAAT,CACLzL,SADK,EAEL0G,KAFK,EAGLW,MAHK,EAII;EACT,MAAM9B,aAAa,GAAGL,gBAAgB,CAAClF,SAAD,CAAtC;EACA,MAAM0L,cAAc,GAAG,CAAC3Q,IAAD,EAAOG,GAAP,EAAY2C,OAAZ,CAAoB0H,aAApB,KAAsC,CAAtC,GAA0C,CAAC,CAA3C,GAA+C,CAAtE;;EAFS,aAKP,OAAO8B,MAAP,KAAkB,UAAlB,GACIA,MAAM,mBACDX,KADC;EAEJ1G,IAAAA,SAAS,EAATA;EAFI,KADV,GAKIqH,MAVG;EAAA,MAIJsE,QAJI;EAAA,MAIMC,QAJN;;EAYTD,EAAAA,QAAQ,GAAGA,QAAQ,IAAI,CAAvB;EACAC,EAAAA,QAAQ,GAAG,CAACA,QAAQ,IAAI,CAAb,IAAkBF,cAA7B;EAEA,SAAO,CAAC3Q,IAAD,EAAOK,KAAP,EAAcyC,OAAd,CAAsB0H,aAAtB,KAAwC,CAAxC,GACH;EAAEzK,IAAAA,CAAC,EAAE8Q,QAAL;EAAe3Q,IAAAA,CAAC,EAAE0Q;EAAlB,GADG,GAEH;EAAE7Q,IAAAA,CAAC,EAAE6Q,QAAL;EAAe1Q,IAAAA,CAAC,EAAE2Q;EAAlB,GAFJ;EAGD;;EAED,SAASvE,MAAT,QAAsE;EAAA,MAApDjB,KAAoD,SAApDA,KAAoD;EAAA,MAA7CvD,OAA6C,SAA7CA,OAA6C;EAAA,MAApCxB,IAAoC,SAApCA,IAAoC;EAAA,wBACxCwB,OADwC,CAC5DwE,MAD4D;EAAA,MAC5DA,MAD4D,gCACnD,CAAC,CAAD,EAAI,CAAJ,CADmD;EAGpE,MAAMvE,IAAI,GAAG7C,UAAU,CAACH,MAAX,CAAkB,UAACC,GAAD,EAAMC,SAAN,EAAoB;EACjDD,IAAAA,GAAG,CAACC,SAAD,CAAH,GAAiByL,uBAAuB,CAACzL,SAAD,EAAYoG,KAAK,CAACM,KAAlB,EAAyBW,MAAzB,CAAxC;EACA,WAAOtH,GAAP;EACD,GAHY,EAGV,EAHU,CAAb;EAHoE,8BAQnD+C,IAAI,CAACsD,KAAK,CAACpG,SAAP,CAR+C;EAAA,MAQ5DlF,CAR4D,yBAQ5DA,CAR4D;EAAA,MAQzDG,CARyD,yBAQzDA,CARyD;;EAUpE,MAAImL,KAAK,CAACgB,aAAN,CAAoBL,aAApB,IAAqC,IAAzC,EAA+C;EAC7CX,IAAAA,KAAK,CAACgB,aAAN,CAAoBL,aAApB,CAAkCjM,CAAlC,IAAuCA,CAAvC;EACAsL,IAAAA,KAAK,CAACgB,aAAN,CAAoBL,aAApB,CAAkC9L,CAAlC,IAAuCA,CAAvC;EACD;;EAEDmL,EAAAA,KAAK,CAACgB,aAAN,CAAoB/F,IAApB,IAA4ByB,IAA5B;EACD;;;AAID,iBAAgB;EACdzB,EAAAA,IAAI,EAAE,QADQ;EAEdsH,EAAAA,OAAO,EAAE,IAFK;EAGd1G,EAAAA,KAAK,EAAE,MAHO;EAIdT,EAAAA,QAAQ,EAAE,CAAC,eAAD,CAJI;EAKdW,EAAAA,EAAE,EAAEkF;EALU,CAAhB;;EC7DA,IAAMwE,MAAI,GAAG;EAAE9Q,EAAAA,IAAI,EAAE,OAAR;EAAiBK,EAAAA,KAAK,EAAE,MAAxB;EAAgCC,EAAAA,MAAM,EAAE,KAAxC;EAA+CH,EAAAA,GAAG,EAAE;EAApD,CAAb;EAEe,SAAS4Q,oBAAT,CAA8B9L,SAA9B,EAA+D;EAC5E,SAAQA,SAAS,CAAC+L,OAAV,CACN,wBADM,EAEN,UAAAC,OAAO;EAAA,WAAIH,MAAI,CAACG,OAAD,CAAR;EAAA,GAFD,CAAR;EAID;;ECPD,IAAMH,IAAI,GAAG;EAAEtM,EAAAA,KAAK,EAAE,KAAT;EAAgBC,EAAAA,GAAG,EAAE;EAArB,CAAb;EAEe,SAASyM,6BAAT,CACbjM,SADa,EAEF;EACX,SAAQA,SAAS,CAAC+L,OAAV,CAAkB,YAAlB,EAAgC,UAAAC,OAAO;EAAA,WAAIH,IAAI,CAACG,OAAD,CAAR;EAAA,GAAvC,CAAR;EACD;;ECmBc,SAASE,oBAAT,CACb9F,KADa,EAEbvD,OAFa,EAGa;EAAA,MAD1BA,OAC0B;EAD1BA,IAAAA,OAC0B,GADP,EACO;EAAA;;EAAA,iBAQtBA,OARsB;EAAA,MAExB7C,SAFwB,YAExBA,SAFwB;EAAA,MAGxB4E,QAHwB,YAGxBA,QAHwB;EAAA,MAIxBC,YAJwB,YAIxBA,YAJwB;EAAA,MAKxB0B,OALwB,YAKxBA,OALwB;EAAA,MAMxB4F,cANwB,YAMxBA,cANwB;EAAA,uCAOxBC,qBAPwB;EAAA,MAOxBA,qBAPwB,sCAOAC,UAPA;EAU1B,MAAM7G,SAAS,GAAGJ,YAAY,CAACpF,SAAD,CAA9B;EAEA,MAAMC,YAAU,GAAGuF,SAAS,GACxB2G,cAAc,GACZtM,mBADY,GAEZA,mBAAmB,CAACV,MAApB,CACE,UAACa,SAAD;EAAA,WAAeoF,YAAY,CAACpF,SAAD,CAAZ,KAA4BwF,SAA3C;EAAA,GADF,CAHsB,GAMxBlG,cANJ;EAQA,MAAIgN,iBAAiB,GAAGrM,YAAU,CAACd,MAAX,CACtB,UAACa,SAAD;EAAA,WAAeoM,qBAAqB,CAACvO,OAAtB,CAA8BmC,SAA9B,KAA4C,CAA3D;EAAA,GADsB,CAAxB;;EAIA,MAAIsM,iBAAiB,CAACrD,MAAlB,KAA6B,CAAjC,EAAoC;EAClCqD,IAAAA,iBAAiB,GAAGrM,YAApB;EACD,GA1ByB;;;EA6B1B,MAAMsM,SAAuB,GAAGD,iBAAiB,CAACxM,MAAlB,CAAyB,UAACC,GAAD,EAAMC,SAAN,EAAoB;EAC3ED,IAAAA,GAAG,CAACC,SAAD,CAAH,GAAiBmG,cAAc,CAACC,KAAD,EAAQ;EACrCpG,MAAAA,SAAS,EAATA,SADqC;EAErC4E,MAAAA,QAAQ,EAARA,QAFqC;EAGrCC,MAAAA,YAAY,EAAZA,YAHqC;EAIrC0B,MAAAA,OAAO,EAAPA;EAJqC,KAAR,CAAd,CAKdrB,gBAAgB,CAAClF,SAAD,CALF,CAAjB;EAOA,WAAOD,GAAP;EACD,GAT+B,EAS7B,EAT6B,CAAhC;EAWA,SAAOgD,MAAM,CAACC,IAAP,CAAYuJ,SAAZ,EAAuBjL,IAAvB,CAA4B,UAACkL,CAAD,EAAIC,CAAJ;EAAA,WAAUF,SAAS,CAACC,CAAD,CAAT,GAAeD,SAAS,CAACE,CAAD,CAAlC;EAAA,GAA5B,CAAP;EACD;;EChDD,SAASC,6BAAT,CAAuC1M,SAAvC,EAA+E;EAC7E,MAAIkF,gBAAgB,CAAClF,SAAD,CAAhB,KAAgCX,IAApC,EAA0C;EACxC,WAAO,EAAP;EACD;;EAED,MAAMsN,iBAAiB,GAAGb,oBAAoB,CAAC9L,SAAD,CAA9C;EAEA,SAAO,CACLiM,6BAA6B,CAACjM,SAAD,CADxB,EAEL2M,iBAFK,EAGLV,6BAA6B,CAACU,iBAAD,CAHxB,CAAP;EAKD;;EAED,SAASC,IAAT,OAAoE;EAAA,MAApDxG,KAAoD,QAApDA,KAAoD;EAAA,MAA7CvD,OAA6C,QAA7CA,OAA6C;EAAA,MAApCxB,IAAoC,QAApCA,IAAoC;;EAClE,MAAI+E,KAAK,CAACgB,aAAN,CAAoB/F,IAApB,EAA0BwL,KAA9B,EAAqC;EACnC;EACD;;EAHiE,0BAe9DhK,OAf8D,CAMhE8C,QANgE;EAAA,MAMtDmH,aANsD,kCAMtC,IANsC;EAAA,yBAe9DjK,OAf8D,CAOhEkK,OAPgE;EAAA,MAOvDC,YAPuD,iCAOxC,IAPwC;EAAA,MAQ5CC,2BAR4C,GAe9DpK,OAf8D,CAQhEqK,kBARgE;EAAA,MAShE3G,OATgE,GAe9D1D,OAf8D,CAShE0D,OATgE;EAAA,MAUhE3B,QAVgE,GAe9D/B,OAf8D,CAUhE+B,QAVgE;EAAA,MAWhEC,YAXgE,GAe9DhC,OAf8D,CAWhEgC,YAXgE;EAAA,MAYhEyB,WAZgE,GAe9DzD,OAf8D,CAYhEyD,WAZgE;EAAA,8BAe9DzD,OAf8D,CAahEsJ,cAbgE;EAAA,MAahEA,cAbgE,sCAa/C,IAb+C;EAAA,MAchEC,qBAdgE,GAe9DvJ,OAf8D,CAchEuJ,qBAdgE;EAiBlE,MAAMe,kBAAkB,GAAG/G,KAAK,CAACvD,OAAN,CAAc7C,SAAzC;EACA,MAAMuF,aAAa,GAAGL,gBAAgB,CAACiI,kBAAD,CAAtC;EACA,MAAMC,eAAe,GAAG7H,aAAa,KAAK4H,kBAA1C;EAEA,MAAMD,kBAAkB,GACtBD,2BAA2B,KAC1BG,eAAe,IAAI,CAACjB,cAApB,GACG,CAACL,oBAAoB,CAACqB,kBAAD,CAArB,CADH,GAEGT,6BAA6B,CAACS,kBAAD,CAHN,CAD7B;EAMA,MAAMlN,UAAU,GAAG,CAACkN,kBAAD,SAAwBD,kBAAxB,EAA4CpN,MAA5C,CACjB,UAACC,GAAD,EAAMC,SAAN,EAAoB;EAClB,WAAOD,GAAG,CAAC3B,MAAJ,CACL8G,gBAAgB,CAAClF,SAAD,CAAhB,KAAgCX,IAAhC,GACI6M,oBAAoB,CAAC9F,KAAD,EAAQ;EAC1BpG,MAAAA,SAAS,EAATA,SAD0B;EAE1B4E,MAAAA,QAAQ,EAARA,QAF0B;EAG1BC,MAAAA,YAAY,EAAZA,YAH0B;EAI1B0B,MAAAA,OAAO,EAAPA,OAJ0B;EAK1B4F,MAAAA,cAAc,EAAdA,cAL0B;EAM1BC,MAAAA,qBAAqB,EAArBA;EAN0B,KAAR,CADxB,GASIpM,SAVC,CAAP;EAYD,GAdgB,EAejB,EAfiB,CAAnB;EAkBA,MAAMqN,aAAa,GAAGjH,KAAK,CAACM,KAAN,CAAY9G,SAAlC;EACA,MAAM6G,UAAU,GAAGL,KAAK,CAACM,KAAN,CAAY/G,MAA/B;EAEA,MAAM2N,SAAS,GAAG,IAAIxM,GAAJ,EAAlB;EACA,MAAIyM,kBAAkB,GAAG,IAAzB;EACA,MAAIC,qBAAqB,GAAGvN,UAAU,CAAC,CAAD,CAAtC;;EAEA,OAAK,IAAIwN,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGxN,UAAU,CAACgJ,MAA/B,EAAuCwE,CAAC,EAAxC,EAA4C;EAC1C,QAAMzN,SAAS,GAAGC,UAAU,CAACwN,CAAD,CAA5B;;EACA,QAAMlI,cAAa,GAAGL,gBAAgB,CAAClF,SAAD,CAAtC;;EACA,QAAM0N,gBAAgB,GAAGtI,YAAY,CAACpF,SAAD,CAAZ,KAA4BT,KAArD;EACA,QAAMoO,UAAU,GAAG,CAACzS,GAAD,EAAMG,MAAN,EAAcwC,OAAd,CAAsB0H,cAAtB,KAAwC,CAA3D;EACA,QAAMK,GAAG,GAAG+H,UAAU,GAAG,OAAH,GAAa,QAAnC;EAEA,QAAMpR,QAAQ,GAAG4J,cAAc,CAACC,KAAD,EAAQ;EACrCpG,MAAAA,SAAS,EAATA,SADqC;EAErC4E,MAAAA,QAAQ,EAARA,QAFqC;EAGrCC,MAAAA,YAAY,EAAZA,YAHqC;EAIrCyB,MAAAA,WAAW,EAAXA,WAJqC;EAKrCC,MAAAA,OAAO,EAAPA;EALqC,KAAR,CAA/B;EAQA,QAAIqH,iBAAsB,GAAGD,UAAU,GACnCD,gBAAgB,GACdtS,KADc,GAEdL,IAHiC,GAInC2S,gBAAgB,GAChBrS,MADgB,GAEhBH,GANJ;;EAQA,QAAImS,aAAa,CAACzH,GAAD,CAAb,GAAqBa,UAAU,CAACb,GAAD,CAAnC,EAA0C;EACxCgI,MAAAA,iBAAiB,GAAG9B,oBAAoB,CAAC8B,iBAAD,CAAxC;EACD;;EAED,QAAMC,gBAAqB,GAAG/B,oBAAoB,CAAC8B,iBAAD,CAAlD;EAEA,QAAME,MAAM,GAAG,EAAf;;EAEA,QAAIhB,aAAJ,EAAmB;EACjBgB,MAAAA,MAAM,CAAChM,IAAP,CAAYvF,QAAQ,CAACgJ,cAAD,CAAR,IAA2B,CAAvC;EACD;;EAED,QAAIyH,YAAJ,EAAkB;EAChBc,MAAAA,MAAM,CAAChM,IAAP,CACEvF,QAAQ,CAACqR,iBAAD,CAAR,IAA+B,CADjC,EAEErR,QAAQ,CAACsR,gBAAD,CAAR,IAA8B,CAFhC;EAID;;EAED,QAAIC,MAAM,CAACC,KAAP,CAAa,UAACC,KAAD;EAAA,aAAWA,KAAX;EAAA,KAAb,CAAJ,EAAoC;EAClCR,MAAAA,qBAAqB,GAAGxN,SAAxB;EACAuN,MAAAA,kBAAkB,GAAG,KAArB;EACA;EACD;;EAEDD,IAAAA,SAAS,CAAClM,GAAV,CAAcpB,SAAd,EAAyB8N,MAAzB;EACD;;EAED,MAAIP,kBAAJ,EAAwB;EACtB;EACA,QAAMU,cAAc,GAAG9B,cAAc,GAAG,CAAH,GAAO,CAA5C;;EAFsB,+BAIbsB,EAJa;EAKpB,UAAMS,gBAAgB,GAAGjO,UAAU,CAACkO,IAAX,CAAgB,UAACnO,SAAD,EAAe;EACtD,YAAM8N,MAAM,GAAGR,SAAS,CAACzL,GAAV,CAAc7B,SAAd,CAAf;;EACA,YAAI8N,MAAJ,EAAY;EACV,iBAAOA,MAAM,CAACM,KAAP,CAAa,CAAb,EAAgBX,EAAhB,EAAmBM,KAAnB,CAAyB,UAACC,KAAD;EAAA,mBAAWA,KAAX;EAAA,WAAzB,CAAP;EACD;EACF,OALwB,CAAzB;;EAOA,UAAIE,gBAAJ,EAAsB;EACpBV,QAAAA,qBAAqB,GAAGU,gBAAxB;EACA;EACD;EAfmB;;EAItB,SAAK,IAAIT,EAAC,GAAGQ,cAAb,EAA6BR,EAAC,GAAG,CAAjC,EAAoCA,EAAC,EAArC,EAAyC;EAAA,uBAAhCA,EAAgC;;EAAA,4BAUrC;EAEH;EACF;;EAED,MAAIrH,KAAK,CAACpG,SAAN,KAAoBwN,qBAAxB,EAA+C;EAC7CpH,IAAAA,KAAK,CAACgB,aAAN,CAAoB/F,IAApB,EAA0BwL,KAA1B,GAAkC,IAAlC;EACAzG,IAAAA,KAAK,CAACpG,SAAN,GAAkBwN,qBAAlB;EACApH,IAAAA,KAAK,CAAC2C,KAAN,GAAc,IAAd;EACD;EACF;;;AAID,eAAgB;EACd1H,EAAAA,IAAI,EAAE,MADQ;EAEdsH,EAAAA,OAAO,EAAE,IAFK;EAGd1G,EAAAA,KAAK,EAAE,MAHO;EAIdE,EAAAA,EAAE,EAAEyK,IAJU;EAKdnL,EAAAA,gBAAgB,EAAE,CAAC,QAAD,CALJ;EAMdqB,EAAAA,IAAI,EAAE;EAAE+J,IAAAA,KAAK,EAAE;EAAT;EANQ,CAAhB;;ECvKe,SAASwB,UAAT,CAAoB9G,IAApB,EAAgD;EAC7D,SAAOA,IAAI,KAAK,GAAT,GAAe,GAAf,GAAqB,GAA5B;EACD;;ECDM,SAAS+G,MAAT,CAAgBtV,KAAhB,EAA6BiN,KAA7B,EAA4CnN,KAA5C,EAAiE;EACtE,SAAOyV,GAAO,CAACvV,KAAD,EAAMwV,GAAO,CAACvI,KAAD,EAAQnN,KAAR,CAAb,CAAd;EACD;EAEM,SAAS2V,cAAT,CAAwBzV,GAAxB,EAAqCiN,KAArC,EAAoDnN,GAApD,EAAiE;EACtE,MAAM4V,CAAC,GAAGJ,MAAM,CAACtV,GAAD,EAAMiN,KAAN,EAAanN,GAAb,CAAhB;EACA,SAAO4V,CAAC,GAAG5V,GAAJ,GAAUA,GAAV,GAAgB4V,CAAvB;EACD;;ECqCD,SAASC,eAAT,OAA+E;EAAA,MAApDvI,KAAoD,QAApDA,KAAoD;EAAA,MAA7CvD,OAA6C,QAA7CA,OAA6C;EAAA,MAApCxB,IAAoC,QAApCA,IAAoC;EAAA,0BAUzEwB,OAVyE,CAE3E8C,QAF2E;EAAA,MAEjEmH,aAFiE,kCAEjD,IAFiD;EAAA,yBAUzEjK,OAVyE,CAG3EkK,OAH2E;EAAA,MAGlEC,YAHkE,iCAGnD,KAHmD;EAAA,MAI3EpI,QAJ2E,GAUzE/B,OAVyE,CAI3E+B,QAJ2E;EAAA,MAK3EC,YAL2E,GAUzEhC,OAVyE,CAK3EgC,YAL2E;EAAA,MAM3EyB,WAN2E,GAUzEzD,OAVyE,CAM3EyD,WAN2E;EAAA,MAO3EC,OAP2E,GAUzE1D,OAVyE,CAO3E0D,OAP2E;EAAA,wBAUzE1D,OAVyE,CAQ3E+L,MAR2E;EAAA,MAQ3EA,MAR2E,gCAQlE,IARkE;EAAA,8BAUzE/L,OAVyE,CAS3EgM,YAT2E;EAAA,MAS3EA,YAT2E,sCAS5D,CAT4D;EAY7E,MAAMtS,QAAQ,GAAG4J,cAAc,CAACC,KAAD,EAAQ;EACrCxB,IAAAA,QAAQ,EAARA,QADqC;EAErCC,IAAAA,YAAY,EAAZA,YAFqC;EAGrC0B,IAAAA,OAAO,EAAPA,OAHqC;EAIrCD,IAAAA,WAAW,EAAXA;EAJqC,GAAR,CAA/B;EAMA,MAAMf,aAAa,GAAGL,gBAAgB,CAACkB,KAAK,CAACpG,SAAP,CAAtC;EACA,MAAMwF,SAAS,GAAGJ,YAAY,CAACgB,KAAK,CAACpG,SAAP,CAA9B;EACA,MAAMoN,eAAe,GAAG,CAAC5H,SAAzB;EACA,MAAMG,QAAQ,GAAGN,wBAAwB,CAACE,aAAD,CAAzC;EACA,MAAMwH,OAAO,GAAGsB,UAAU,CAAC1I,QAAD,CAA1B;EACA,MAAMoB,aAAa,GAAGX,KAAK,CAACgB,aAAN,CAAoBL,aAA1C;EACA,MAAMsG,aAAa,GAAGjH,KAAK,CAACM,KAAN,CAAY9G,SAAlC;EACA,MAAM6G,UAAU,GAAGL,KAAK,CAACM,KAAN,CAAY/G,MAA/B;EACA,MAAMmP,iBAAiB,GACrB,OAAOD,YAAP,KAAwB,UAAxB,GACIA,YAAY,mBACPzI,KAAK,CAACM,KADC;EAEV1G,IAAAA,SAAS,EAAEoG,KAAK,CAACpG;EAFP,KADhB,GAKI6O,YANN;EAOA,MAAME,2BAA2B,GAC/B,OAAOD,iBAAP,KAA6B,QAA7B,GACI;EAAEnJ,IAAAA,QAAQ,EAAEmJ,iBAAZ;EAA+B/B,IAAAA,OAAO,EAAE+B;EAAxC,GADJ;EAEMnJ,IAAAA,QAAQ,EAAE,CAFhB;EAEmBoH,IAAAA,OAAO,EAAE;EAF5B,KAEkC+B,iBAFlC,CADF;EAIA,MAAME,mBAAmB,GAAG5I,KAAK,CAACgB,aAAN,CAAoBC,MAApB,GACxBjB,KAAK,CAACgB,aAAN,CAAoBC,MAApB,CAA2BjB,KAAK,CAACpG,SAAjC,CADwB,GAExB,IAFJ;EAIA,MAAM8C,IAAI,GAAG;EAAEhI,IAAAA,CAAC,EAAE,CAAL;EAAQG,IAAAA,CAAC,EAAE;EAAX,GAAb;;EAEA,MAAI,CAAC8L,aAAL,EAAoB;EAClB;EACD;;EAED,MAAI+F,aAAJ,EAAmB;EAAA;;EACjB,QAAMmC,QAAQ,GAAGtJ,QAAQ,KAAK,GAAb,GAAmBzK,GAAnB,GAAyBH,IAA1C;EACA,QAAMmU,OAAO,GAAGvJ,QAAQ,KAAK,GAAb,GAAmBtK,MAAnB,GAA4BD,KAA5C;EACA,QAAMwK,GAAG,GAAGD,QAAQ,KAAK,GAAb,GAAmB,QAAnB,GAA8B,OAA1C;EACA,QAAM0B,MAAM,GAAGN,aAAa,CAACpB,QAAD,CAA5B;EAEA,QAAM3M,KAAG,GAAGqO,MAAM,GAAG9K,QAAQ,CAAC0S,QAAD,CAA7B;EACA,QAAMnW,KAAG,GAAGuO,MAAM,GAAG9K,QAAQ,CAAC2S,OAAD,CAA7B;EAEA,QAAMC,QAAQ,GAAGP,MAAM,GAAG,CAACnI,UAAU,CAACb,GAAD,CAAX,GAAmB,CAAtB,GAA0B,CAAjD;EAEA,QAAMwJ,MAAM,GAAG5J,SAAS,KAAKjG,KAAd,GAAsB8N,aAAa,CAACzH,GAAD,CAAnC,GAA2Ca,UAAU,CAACb,GAAD,CAApE;EACA,QAAMyJ,MAAM,GAAG7J,SAAS,KAAKjG,KAAd,GAAsB,CAACkH,UAAU,CAACb,GAAD,CAAjC,GAAyC,CAACyH,aAAa,CAACzH,GAAD,CAAtE,CAZiB;EAejB;;EACA,QAAM0J,YAAY,GAAGlJ,KAAK,CAACO,QAAN,CAAemE,KAApC;EACA,QAAMyE,SAAS,GACbX,MAAM,IAAIU,YAAV,GACIhS,aAAa,CAACgS,YAAD,CADjB,GAEI;EAAE7U,MAAAA,KAAK,EAAE,CAAT;EAAYE,MAAAA,MAAM,EAAE;EAApB,KAHN;EAIA,QAAM6U,kBAAkB,GAAGpJ,KAAK,CAACgB,aAAN,CAAoB,kBAApB,IACvBhB,KAAK,CAACgB,aAAN,CAAoB,kBAApB,EAAwCb,OADjB,GAEvBV,kBAAkB,EAFtB;EAGA,QAAM4J,eAAe,GAAGD,kBAAkB,CAACP,QAAD,CAA1C;EACA,QAAMS,eAAe,GAAGF,kBAAkB,CAACN,OAAD,CAA1C,CAzBiB;EA4BjB;EACA;EACA;EACA;;EACA,QAAMS,QAAQ,GAAGrB,MAAM,CAAC,CAAD,EAAIjB,aAAa,CAACzH,GAAD,CAAjB,EAAwB2J,SAAS,CAAC3J,GAAD,CAAjC,CAAvB;EAEA,QAAMgK,SAAS,GAAGxC,eAAe,GAC7BC,aAAa,CAACzH,GAAD,CAAb,GAAqB,CAArB,GACAuJ,QADA,GAEAQ,QAFA,GAGAF,eAHA,GAIAV,2BAA2B,CAACpJ,QALC,GAM7ByJ,MAAM,GACNO,QADA,GAEAF,eAFA,GAGAV,2BAA2B,CAACpJ,QAThC;EAUA,QAAMkK,SAAS,GAAGzC,eAAe,GAC7B,CAACC,aAAa,CAACzH,GAAD,CAAd,GAAsB,CAAtB,GACAuJ,QADA,GAEAQ,QAFA,GAGAD,eAHA,GAIAX,2BAA2B,CAACpJ,QALC,GAM7B0J,MAAM,GACNM,QADA,GAEAD,eAFA,GAGAX,2BAA2B,CAACpJ,QAThC;EAWA,QAAMmK,iBAAiB,GACrB1J,KAAK,CAACO,QAAN,CAAemE,KAAf,IAAwB1L,eAAe,CAACgH,KAAK,CAACO,QAAN,CAAemE,KAAhB,CADzC;EAEA,QAAMiF,YAAY,GAAGD,iBAAiB,GAClCnK,QAAQ,KAAK,GAAb,GACEmK,iBAAiB,CAACzS,SAAlB,IAA+B,CADjC,GAEEyS,iBAAiB,CAAC1S,UAAlB,IAAgC,CAHA,GAIlC,CAJJ;EAMA,QAAM4S,mBAAmB,4BAAGhB,mBAAH,oBAAGA,mBAAmB,CAAGrJ,QAAH,CAAtB,oCAAsC,CAA/D;EACA,QAAMsK,SAAS,GAAG5I,MAAM,GAAGuI,SAAT,GAAqBI,mBAArB,GAA2CD,YAA7D;EACA,QAAMG,SAAS,GAAG7I,MAAM,GAAGwI,SAAT,GAAqBG,mBAAvC;EAEA,QAAMG,eAAe,GAAG7B,MAAM,CAC5BM,MAAM,GAAGJ,GAAO,CAACxV,KAAD,EAAMiX,SAAN,CAAV,GAA6BjX,KADP,EAE5BqO,MAF4B,EAG5BuH,MAAM,GAAGL,GAAO,CAACzV,KAAD,EAAMoX,SAAN,CAAV,GAA6BpX,KAHP,CAA9B;EAMAiO,IAAAA,aAAa,CAACpB,QAAD,CAAb,GAA0BwK,eAA1B;EACArN,IAAAA,IAAI,CAAC6C,QAAD,CAAJ,GAAiBwK,eAAe,GAAG9I,MAAnC;EACD;;EAED,MAAI2F,YAAJ,EAAkB;EAAA;;EAChB,QAAMiC,SAAQ,GAAGtJ,QAAQ,KAAK,GAAb,GAAmBzK,GAAnB,GAAyBH,IAA1C;;EACA,QAAMmU,QAAO,GAAGvJ,QAAQ,KAAK,GAAb,GAAmBtK,MAAnB,GAA4BD,KAA5C;;EACA,QAAMiM,OAAM,GAAGN,aAAa,CAACgG,OAAD,CAA5B;;EAEA,QAAMnH,IAAG,GAAGmH,OAAO,KAAK,GAAZ,GAAkB,QAAlB,GAA6B,OAAzC;;EAEA,QAAM/T,IAAG,GAAGqO,OAAM,GAAG9K,QAAQ,CAAC0S,SAAD,CAA7B;;EACA,QAAMnW,IAAG,GAAGuO,OAAM,GAAG9K,QAAQ,CAAC2S,QAAD,CAA7B;;EAEA,QAAMkB,YAAY,GAAG,CAAClV,GAAD,EAAMH,IAAN,EAAY8C,OAAZ,CAAoB0H,aAApB,MAAuC,CAAC,CAA7D;;EAEA,QAAMyK,oBAAmB,6BAAGhB,mBAAH,oBAAGA,mBAAmB,CAAGjC,OAAH,CAAtB,qCAAqC,CAA9D;;EACA,QAAMkD,UAAS,GAAGG,YAAY,GAC1BpX,IAD0B,GAE1BqO,OAAM,GACNgG,aAAa,CAACzH,IAAD,CADb,GAEAa,UAAU,CAACb,IAAD,CAFV,GAGAoK,oBAHA,GAIAjB,2BAA2B,CAAChC,OANhC;;EAOA,QAAMmD,UAAS,GAAGE,YAAY,GAC1B/I,OAAM,GACNgG,aAAa,CAACzH,IAAD,CADb,GAEAa,UAAU,CAACb,IAAD,CAFV,GAGAoK,oBAHA,GAIAjB,2BAA2B,CAAChC,OALF,GAM1BjU,IANJ;;EAQA,QAAMqX,gBAAe,GACnBvB,MAAM,IAAIwB,YAAV,GACI3B,cAAc,CAACwB,UAAD,EAAY5I,OAAZ,EAAoB6I,UAApB,CADlB,GAEI5B,MAAM,CAACM,MAAM,GAAGqB,UAAH,GAAejX,IAAtB,EAA2BqO,OAA3B,EAAmCuH,MAAM,GAAGsB,UAAH,GAAepX,IAAxD,CAHZ;;EAKAiO,IAAAA,aAAa,CAACgG,OAAD,CAAb,GAAyBoD,gBAAzB;EACArN,IAAAA,IAAI,CAACiK,OAAD,CAAJ,GAAgBoD,gBAAe,GAAG9I,OAAlC;EACD;;EAEDjB,EAAAA,KAAK,CAACgB,aAAN,CAAoB/F,IAApB,IAA4ByB,IAA5B;EACD;;;AAID,0BAAgB;EACdzB,EAAAA,IAAI,EAAE,iBADQ;EAEdsH,EAAAA,OAAO,EAAE,IAFK;EAGd1G,EAAAA,KAAK,EAAE,MAHO;EAIdE,EAAAA,EAAE,EAAEwM,eAJU;EAKdlN,EAAAA,gBAAgB,EAAE,CAAC,QAAD;EALJ,CAAhB;;EC5LA,IAAM4O,eAAe,GAAG,SAAlBA,eAAkB,CAAC9J,OAAD,EAAUH,KAAV,EAAoB;EAC1CG,EAAAA,OAAO,GACL,OAAOA,OAAP,KAAmB,UAAnB,GACIA,OAAO,mBAAMH,KAAK,CAACM,KAAZ;EAAmB1G,IAAAA,SAAS,EAAEoG,KAAK,CAACpG;EAApC,KADX,GAEIuG,OAHN;EAKA,SAAOT,kBAAkB,CACvB,OAAOS,OAAP,KAAmB,QAAnB,GACIA,OADJ,GAEIP,eAAe,CAACO,OAAD,EAAUjH,cAAV,CAHI,CAAzB;EAKD,CAXD;;EAaA,SAASwL,KAAT,OAAqE;EAAA;;EAAA,MAApD1E,KAAoD,QAApDA,KAAoD;EAAA,MAA7C/E,IAA6C,QAA7CA,IAA6C;EAAA,MAAvCwB,OAAuC,QAAvCA,OAAuC;EACnE,MAAMyM,YAAY,GAAGlJ,KAAK,CAACO,QAAN,CAAemE,KAApC;EACA,MAAM/D,aAAa,GAAGX,KAAK,CAACgB,aAAN,CAAoBL,aAA1C;EACA,MAAMxB,aAAa,GAAGL,gBAAgB,CAACkB,KAAK,CAACpG,SAAP,CAAtC;EACA,MAAMuH,IAAI,GAAGlC,wBAAwB,CAACE,aAAD,CAArC;EACA,MAAMoI,UAAU,GAAG,CAAC5S,IAAD,EAAOK,KAAP,EAAcyC,OAAd,CAAsB0H,aAAtB,KAAwC,CAA3D;EACA,MAAMK,GAAG,GAAG+H,UAAU,GAAG,QAAH,GAAc,OAApC;;EAEA,MAAI,CAAC2B,YAAD,IAAiB,CAACvI,aAAtB,EAAqC;EACnC;EACD;;EAED,MAAMhB,aAAa,GAAGsK,eAAe,CAACxN,OAAO,CAAC0D,OAAT,EAAkBH,KAAlB,CAArC;EACA,MAAMmJ,SAAS,GAAGjS,aAAa,CAACgS,YAAD,CAA/B;EACA,MAAMgB,OAAO,GAAG/I,IAAI,KAAK,GAAT,GAAerM,GAAf,GAAqBH,IAArC;EACA,MAAMwV,OAAO,GAAGhJ,IAAI,KAAK,GAAT,GAAelM,MAAf,GAAwBD,KAAxC;EAEA,MAAMoV,OAAO,GACXpK,KAAK,CAACM,KAAN,CAAY9G,SAAZ,CAAsBgG,GAAtB,IACAQ,KAAK,CAACM,KAAN,CAAY9G,SAAZ,CAAsB2H,IAAtB,CADA,GAEAR,aAAa,CAACQ,IAAD,CAFb,GAGAnB,KAAK,CAACM,KAAN,CAAY/G,MAAZ,CAAmBiG,GAAnB,CAJF;EAKA,MAAM6K,SAAS,GAAG1J,aAAa,CAACQ,IAAD,CAAb,GAAsBnB,KAAK,CAACM,KAAN,CAAY9G,SAAZ,CAAsB2H,IAAtB,CAAxC;EAEA,MAAMuI,iBAAiB,GAAG1Q,eAAe,CAACkQ,YAAD,CAAzC;EACA,MAAMoB,UAAU,GAAGZ,iBAAiB,GAChCvI,IAAI,KAAK,GAAT,GACEuI,iBAAiB,CAACxM,YAAlB,IAAkC,CADpC,GAEEwM,iBAAiB,CAACzM,WAAlB,IAAiC,CAHH,GAIhC,CAJJ;EAMA,MAAMsN,iBAAiB,GAAGH,OAAO,GAAG,CAAV,GAAcC,SAAS,GAAG,CAApD,CA/BmE;EAkCnE;;EACA,MAAMzX,GAAG,GAAG+M,aAAa,CAACuK,OAAD,CAAzB;EACA,MAAMxX,GAAG,GAAG4X,UAAU,GAAGnB,SAAS,CAAC3J,GAAD,CAAtB,GAA8BG,aAAa,CAACwK,OAAD,CAAvD;EACA,MAAMK,MAAM,GAAGF,UAAU,GAAG,CAAb,GAAiBnB,SAAS,CAAC3J,GAAD,CAAT,GAAiB,CAAlC,GAAsC+K,iBAArD;EACA,MAAMtJ,MAAM,GAAGiH,MAAM,CAACtV,GAAD,EAAM4X,MAAN,EAAc9X,GAAd,CAArB,CAtCmE;;EAyCnE,MAAM+X,QAAgB,GAAGtJ,IAAzB;EACAnB,EAAAA,KAAK,CAACgB,aAAN,CAAoB/F,IAApB,uDACGwP,QADH,IACcxJ,MADd,wBAEEyJ,YAFF,GAEgBzJ,MAAM,GAAGuJ,MAFzB;EAID;;EAED,SAASxH,MAAT,QAAgE;EAAA,MAA9ChD,KAA8C,SAA9CA,KAA8C;EAAA,MAAvCvD,OAAuC,SAAvCA,OAAuC;EAAA,yBACNA,OADM,CACxD3I,OADwD;EAAA,MAC/CoV,YAD+C,iCAChC,qBADgC;;EAG9D,MAAIA,YAAY,IAAI,IAApB,EAA0B;EACxB;EACD,GAL6D;;;EAQ9D,MAAI,OAAOA,YAAP,KAAwB,QAA5B,EAAsC;EACpCA,IAAAA,YAAY,GAAGlJ,KAAK,CAACO,QAAN,CAAehH,MAAf,CAAsBoR,aAAtB,CAAoCzB,YAApC,CAAf;;EAEA,QAAI,CAACA,YAAL,EAAmB;EACjB;EACD;EACF;;EAED,MAAI,CAACzL,QAAQ,CAACuC,KAAK,CAACO,QAAN,CAAehH,MAAhB,EAAwB2P,YAAxB,CAAb,EAAoD;EAClD;EACD;;EAEDlJ,EAAAA,KAAK,CAACO,QAAN,CAAemE,KAAf,GAAuBwE,YAAvB;EACD;;;AAID,gBAAgB;EACdjO,EAAAA,IAAI,EAAE,OADQ;EAEdsH,EAAAA,OAAO,EAAE,IAFK;EAGd1G,EAAAA,KAAK,EAAE,MAHO;EAIdE,EAAAA,EAAE,EAAE2I,KAJU;EAKd1B,EAAAA,MAAM,EAANA,MALc;EAMd5H,EAAAA,QAAQ,EAAE,CAAC,eAAD,CANI;EAOdC,EAAAA,gBAAgB,EAAE,CAAC,iBAAD;EAPJ,CAAhB;;ECpGA,SAASuP,cAAT,CACEzU,QADF,EAEEI,IAFF,EAGEsU,gBAHF,EAIc;EAAA,MADZA,gBACY;EADZA,IAAAA,gBACY,GADgB;EAAEnW,MAAAA,CAAC,EAAE,CAAL;EAAQG,MAAAA,CAAC,EAAE;EAAX,KAChB;EAAA;;EACZ,SAAO;EACLC,IAAAA,GAAG,EAAEqB,QAAQ,CAACrB,GAAT,GAAeyB,IAAI,CAAChC,MAApB,GAA6BsW,gBAAgB,CAAChW,CAD9C;EAELG,IAAAA,KAAK,EAAEmB,QAAQ,CAACnB,KAAT,GAAiBuB,IAAI,CAAClC,KAAtB,GAA8BwW,gBAAgB,CAACnW,CAFjD;EAGLO,IAAAA,MAAM,EAAEkB,QAAQ,CAAClB,MAAT,GAAkBsB,IAAI,CAAChC,MAAvB,GAAgCsW,gBAAgB,CAAChW,CAHpD;EAILF,IAAAA,IAAI,EAAEwB,QAAQ,CAACxB,IAAT,GAAgB4B,IAAI,CAAClC,KAArB,GAA6BwW,gBAAgB,CAACnW;EAJ/C,GAAP;EAMD;;EAED,SAASoW,qBAAT,CAA+B3U,QAA/B,EAA8D;EAC5D,SAAO,CAACrB,GAAD,EAAME,KAAN,EAAaC,MAAb,EAAqBN,IAArB,EAA2B4M,IAA3B,CAAgC,UAACwJ,IAAD;EAAA,WAAU5U,QAAQ,CAAC4U,IAAD,CAAR,IAAkB,CAA5B;EAAA,GAAhC,CAAP;EACD;;EAED,SAASC,IAAT,OAAwD;EAAA,MAAxChL,KAAwC,QAAxCA,KAAwC;EAAA,MAAjC/E,IAAiC,QAAjCA,IAAiC;EACtD,MAAMgM,aAAa,GAAGjH,KAAK,CAACM,KAAN,CAAY9G,SAAlC;EACA,MAAM6G,UAAU,GAAGL,KAAK,CAACM,KAAN,CAAY/G,MAA/B;EACA,MAAMsR,gBAAgB,GAAG7K,KAAK,CAACgB,aAAN,CAAoBuH,eAA7C;EAEA,MAAM0C,iBAAiB,GAAGlL,cAAc,CAACC,KAAD,EAAQ;EAC9CC,IAAAA,cAAc,EAAE;EAD8B,GAAR,CAAxC;EAGA,MAAMiL,iBAAiB,GAAGnL,cAAc,CAACC,KAAD,EAAQ;EAC9CE,IAAAA,WAAW,EAAE;EADiC,GAAR,CAAxC;EAIA,MAAMiL,wBAAwB,GAAGP,cAAc,CAC7CK,iBAD6C,EAE7ChE,aAF6C,CAA/C;EAIA,MAAMmE,mBAAmB,GAAGR,cAAc,CACxCM,iBADwC,EAExC7K,UAFwC,EAGxCwK,gBAHwC,CAA1C;EAMA,MAAMQ,iBAAiB,GAAGP,qBAAqB,CAACK,wBAAD,CAA/C;EACA,MAAMG,gBAAgB,GAAGR,qBAAqB,CAACM,mBAAD,CAA9C;EAEApL,EAAAA,KAAK,CAACgB,aAAN,CAAoB/F,IAApB,IAA4B;EAC1BkQ,IAAAA,wBAAwB,EAAxBA,wBAD0B;EAE1BC,IAAAA,mBAAmB,EAAnBA,mBAF0B;EAG1BC,IAAAA,iBAAiB,EAAjBA,iBAH0B;EAI1BC,IAAAA,gBAAgB,EAAhBA;EAJ0B,GAA5B;EAOAtL,EAAAA,KAAK,CAAC6B,UAAN,CAAiBtI,MAAjB,qBACKyG,KAAK,CAAC6B,UAAN,CAAiBtI,MADtB;EAEE,oCAAgC8R,iBAFlC;EAGE,2BAAuBC;EAHzB;EAKD;;;AAID,eAAgB;EACdrQ,EAAAA,IAAI,EAAE,MADQ;EAEdsH,EAAAA,OAAO,EAAE,IAFK;EAGd1G,EAAAA,KAAK,EAAE,MAHO;EAIdR,EAAAA,gBAAgB,EAAE,CAAC,iBAAD,CAJJ;EAKdU,EAAAA,EAAE,EAAEiP;EALU,CAAhB;;EC3DA,IAAMtJ,kBAAgB,GAAG,CACvB6J,cADuB,EAEvB5K,eAFuB,EAGvB8D,eAHuB,EAIvBE,aAJuB,CAAzB;MAOM/C,cAAY,gBAAGJ,eAAe,CAAC;EAAEE,EAAAA,gBAAgB,EAAhBA;EAAF,CAAD;;MCF9BA,gBAAgB,GAAG,CACvB6J,cADuB,EAEvB5K,eAFuB,EAGvB8D,eAHuB,EAIvBE,aAJuB,EAKvB1D,QALuB,EAMvBuF,MANuB,EAOvB+B,iBAPuB,EAQvB7D,OARuB,EASvBsG,MATuB;MAYnBpJ,YAAY,gBAAGJ,eAAe,CAAC;EAAEE,EAAAA,gBAAgB,EAAhBA;EAAF,CAAD;;;;;;;;;;;;;;;;;;;"}
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Row, Col, Card, Button, Badge, Form, InputGroup, Spinner } from 'react-bootstrap';
import axiosInstance from '../utils/axiosConfig';
import { RECIPE_ROUTES } from '../utils/apiRoutes';
import { toast } from 'react-toastify';

const Recipes = () => {
  const [recipes, setRecipes] = useState([]);
  const [filteredRecipes, setFilteredRecipes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('newest');

  useEffect(() => {
    fetchRecipes();
  }, []);

  useEffect(() => {
    filterAndSortRecipes();
  }, [recipes, searchTerm, sortBy]);

  const fetchRecipes = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(RECIPE_ROUTES.GET_ALL);
      if (response.data.success) {
        setRecipes(response.data.recipes);
      }
    } catch (error) {
      console.error('Error fetching recipes:', error);
      toast.error('Failed to fetch recipes');
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortRecipes = () => {
    let filtered = recipes.filter(recipe =>
      recipe.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      recipe.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      recipe.ingredients.some(ingredient => 
        ingredient.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );

    // Sort recipes
    switch (sortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
        break;
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'steps':
        filtered.sort((a, b) => a.steps.length - b.steps.length);
        break;
      default:
        break;
    }

    setFilteredRecipes(filtered);
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleSortChange = (e) => {
    setSortBy(e.target.value);
  };

  return (
    <>
      {/* Page Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex align-items-center justify-content-between">
            <div>
              <h1 className="h3 mb-1">All Recipes</h1>
              <p className="text-muted mb-0">
                Discover {recipes.length} delicious recipes with voice guidance
              </p>
            </div>
          </div>
        </Col>
      </Row>

      {/* Search and Filter Controls */}
      <Row className="mb-4">
        <Col md={8}>
          <InputGroup size="lg">
            <InputGroup.Text>
              <i className="fas fa-search"></i>
            </InputGroup.Text>
            <Form.Control
              type="text"
              placeholder="Search recipes, ingredients, or descriptions..."
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </InputGroup>
        </Col>
        <Col md={4}>
          <Form.Select size="lg" value={sortBy} onChange={handleSortChange}>
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="name">Name (A-Z)</option>
            <option value="steps">Fewest Steps</option>
          </Form.Select>
        </Col>
      </Row>

      {/* Results Info */}
      <Row className="mb-3">
        <Col>
          <div className="d-flex align-items-center justify-content-between">
            <p className="text-muted mb-0">
              {loading ? 'Loading...' : `Showing ${filteredRecipes.length} of ${recipes.length} recipes`}
            </p>
            {searchTerm && (
              <Button
                variant="outline-secondary"
                size="sm"
                onClick={() => setSearchTerm('')}
              >
                <i className="fas fa-times me-1"></i>
                Clear Search
              </Button>
            )}
          </div>
        </Col>
      </Row>

      {/* Recipes Grid */}
      {loading ? (
        <Row className="justify-content-center">
          <Col md={6} className="text-center py-5">
            <Spinner animation="border" variant="primary" className="mb-3" />
            <p className="text-muted">Loading recipes...</p>
          </Col>
        </Row>
      ) : (
        <Row>
          {filteredRecipes.length > 0 ? (
            filteredRecipes.map((recipe) => (
              <Col xl={3} lg={4} md={6} className="mb-4" key={recipe._id}>
                <Card className="border-0 shadow-sm h-100 recipe-card">
                  <div className="position-relative">
                    <Card.Img
                      variant="top"
                      src={recipe.image}
                      alt={recipe.name}
                      style={{ height: '200px', objectFit: 'cover' }}
                    />
                    <Badge 
                      bg="primary" 
                      className="position-absolute top-0 end-0 m-2"
                    >
                      {recipe.steps?.length || 0} Steps
                    </Badge>
                    <Badge 
                      bg="success" 
                      className="position-absolute top-0 start-0 m-2"
                    >
                      {recipe.ingredients?.length || 0} Ingredients
                    </Badge>
                  </div>
                  <Card.Body className="d-flex flex-column">
                    <Card.Title className="h6 mb-2">{recipe.name}</Card.Title>
                    <Card.Text className="text-muted flex-grow-1 small">
                      {recipe.description.length > 100
                        ? `${recipe.description.substring(0, 100)}...`
                        : recipe.description}
                    </Card.Text>
                    <div className="d-flex align-items-center justify-content-between mt-3">
                      <div className="d-flex align-items-center text-muted small">
                        <img
                          src={recipe.createdBy?.image || '/default-avatar.png'}
                          alt="Chef"
                          className="rounded-circle me-2"
                          style={{ width: '20px', height: '20px', objectFit: 'cover' }}
                        />
                        <span className="text-truncate">
                          {recipe.createdBy?.username || 'Unknown'}
                        </span>
                      </div>
                      <Button
                        as={Link}
                        to={`/recipe/${recipe._id}`}
                        variant="primary"
                        size="sm"
                      >
                        <i className="fas fa-play me-1"></i>
                        Cook
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            ))
          ) : (
            <Col md={12} className="text-center py-5">
              <div className="text-muted">
                {searchTerm ? (
                  <>
                    <i className="fas fa-search fa-3x mb-3"></i>
                    <h4>No recipes found</h4>
                    <p>Try adjusting your search terms or browse all recipes.</p>
                    <Button
                      variant="primary"
                      onClick={() => setSearchTerm('')}
                    >
                      Clear Search
                    </Button>
                  </>
                ) : (
                  <>
                    <i className="fas fa-utensils fa-3x mb-3"></i>
                    <h4>No recipes available</h4>
                    <p>Check back later for new recipes!</p>
                  </>
                )}
              </div>
            </Col>
          )}
        </Row>
      )}
    </>
  );
};

export default Recipes;

{"Commands:": "Comandos:", "Options:": "Opciones:", "Examples:": "Ejemplos:", "boolean": "boolean", "count": "cuenta", "string": "cadena de caracteres", "number": "número", "array": "tabla", "required": "requisito", "default:": "defecto:", "choices:": "selección:", "generated-value": "valor-generado", "Not enough non-option arguments: got %s, need at least %s": "Hacen falta argumentos no-opcionales: Número recibido %s, necesita por lo menos %s", "Too many non-option arguments: got %s, maximum of %s": "Demasiados argumentos no-opcionales: Número recibido %s, máximo es %s", "Missing argument value: %s": {"one": "Falta argumento: %s", "other": "Faltan argumentos: %s"}, "Missing required argument: %s": {"one": "Falta argumento requerido: %s", "other": "Faltan argumentos requeridos: %s"}, "Unknown argument: %s": {"one": "Argumento desconocido: %s", "other": "Argumentos desconocidos: %s"}, "Invalid values:": "Valores inválidos:", "Argument: %s, Given: %s, Choices: %s": "Argumento: %s, Recibido: %s, Selección: %s", "Argument check failed: %s": "Verificación de argumento ha fracasado: %s", "Implications failed:": "Implicaciones fracasadas:", "Not enough arguments following: %s": "No hay suficientes argumentos después de: %s", "Invalid JSON config file: %s": "Archivo de configuración JSON inválido: %s", "Path to JSON config file": "Ruta al archivo de configuración JSON", "Show help": "<PERSON><PERSON><PERSON> a<PERSON>", "Show version number": "Muestra número de versión"}
// Authentication utility functions

export const isAuthenticated = () => {
  // Check if user data exists in localStorage
  const userData = localStorage.getItem('cookify_user');
  return userData !== null;
};

export const getUserData = () => {
  const userData = localStorage.getItem('cookify_user');
  return userData ? JSON.parse(userData) : null;
};

export const setUserData = (user) => {
  localStorage.setItem('cookify_user', JSON.stringify(user));
};

export const clearUserData = () => {
  localStorage.removeItem('cookify_user');
};

export const getUserRole = () => {
  const userData = getUserData();
  return userData ? userData.role : null;
};

export const isUser = () => {
  const role = getUserRole();
  return role === 'User';
};

export const isChef = () => {
  const role = getUserRole();
  return role === 'Chef';
};

export const isAdmin = () => {
  const role = getUserRole();
  return role === 'Admin';
};

export const canCreateRecipes = () => {
  return isChef() || isAdmin();
};

export const canManageAllRecipes = () => {
  return isAdmin();
};

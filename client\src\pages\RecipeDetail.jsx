import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { Row, Col, Card, Button, Badge, ListGroup, Dropdown } from 'react-bootstrap';
import { toast } from 'react-toastify';
import axiosInstance from '../utils/axiosConfig';
import { RECIPE_ROUTES, ASSISTANT_ROUTES } from '../utils/apiRoutes';
import { getUserData, isAdmin, isChef, isUser } from '../utils/auth';
import Timer from '../components/Timer';
import FeedbackSection from '../components/FeedbackSection';
import RecipeModal from '../components/RecipeModal';
import CookingMode from '../components/CookingMode';

const RecipeDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [recipe, setRecipe] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [audioUrl, setAudioUrl] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCookingMode, setShowCookingMode] = useState(false);
  const userData = getUserData();

  const canEditRecipe = () => {
    return isAdmin() || (isChef() && recipe?.createdBy?._id === userData?.id);
  };

  const canDeleteRecipe = () => {
    return isAdmin() || (isChef() && recipe?.createdBy?._id === userData?.id);
  };

  const handleEditRecipe = () => {
    setShowEditModal(true);
  };

  const handleDeleteRecipe = async () => {
    if (!window.confirm('Are you sure you want to delete this recipe? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await axiosInstance.delete(RECIPE_ROUTES.DELETE(recipe._id));
      if (response.data.success) {
        toast.success('Recipe deleted successfully');
        navigate('/my-recipes');
      }
    } catch (error) {
      console.error('Error deleting recipe:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete recipe';
      toast.error(errorMessage);
    }
  };

  const handleModalClose = () => {
    setShowEditModal(false);
    fetchRecipe(); // Refresh recipe data
  };

  const startVoiceCooking = () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      toast.error('Voice recognition is not supported in this browser. Please use Chrome or Edge.');
      return;
    }
    if (!('speechSynthesis' in window)) {
      toast.error('Speech synthesis is not supported in this browser.');
      return;
    }
    setShowCookingMode(true);
  };

  const closeCookingMode = () => {
    setShowCookingMode(false);
  };

  useEffect(() => {
    fetchRecipe();
  }, [id]);

  const fetchRecipe = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(RECIPE_ROUTES.GET_BY_ID(id));
      if (response.data.success) {
        setRecipe(response.data.recipe);
      }
    } catch (error) {
      console.error('Error fetching recipe:', error);
      toast.error('Failed to fetch recipe details');
    } finally {
      setLoading(false);
    }
  };

  const speakStep = async (stepIndex, action = 'next') => {
    if (!recipe || !recipe.steps || !recipe.steps[stepIndex]) return;

    try {
      setIsSpeaking(true);

      // Call backend voice assistant API
      const response = await axiosInstance.post(ASSISTANT_ROUTES.STEP_VOICE, {
        recipeId: recipe._id,
        stepIndex: stepIndex,
        action: action
      });

      if (response.data.success && response.data.audioUrl) {
        setAudioUrl(response.data.audioUrl);

        // Play the audio
        const audio = new Audio(response.data.audioUrl);
        audio.play().catch(error => {
          console.error('Error playing audio:', error);
          // Fallback to browser speech synthesis
          fallbackSpeak(stepIndex);
        });
      } else {
        // Fallback to browser speech synthesis
        fallbackSpeak(stepIndex);
      }
    } catch (error) {
      console.error('Error with voice assistant:', error);
      // Fallback to browser speech synthesis
      fallbackSpeak(stepIndex);
    } finally {
      setIsSpeaking(false);
    }
  };

  const fallbackSpeak = (stepIndex) => {
    if ('speechSynthesis' in window) {
      const stepText = `Step ${stepIndex + 1}: ${recipe.steps[stepIndex]}`;
      const utterance = new SpeechSynthesisUtterance(stepText);
      utterance.lang = 'hi-IN';
      utterance.rate = 0.8;
      window.speechSynthesis.speak(utterance);
    } else {
      toast.error('Voice synthesis not supported in this browser');
    }
  };

  const handleNextStep = () => {
    if (!recipe.steps || recipe.steps.length === 0) return;

    if (currentStep < recipe.steps.length - 1) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      speakStep(nextStep);
    } else {
      toast.info('You have completed all steps!');
    }
  };

  const handlePreviousStep = () => {
    if (!recipe.steps || recipe.steps.length === 0) return;

    if (currentStep > 0) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      speakStep(prevStep);
    }
  };

  const handleRepeatStep = () => {
    if (!recipe.steps || recipe.steps.length === 0) return;
    speakStep(currentStep);
  };

  const startVoiceListening = async () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      toast.error('Voice recognition not supported in this browser');
      return;
    }

    try {
      setIsListening(true);

      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'hi-IN';

      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript.toLowerCase();
        const command = parseVoiceCommand(transcript);

        switch (command) {
          case 'next':
            handleNextStep();
            break;
          case 'repeat':
            handleRepeatStep();
            break;
          case 'pause':
            window.speechSynthesis.cancel();
            break;
          default:
            toast.info(`Voice command not recognized: ${transcript}`);
        }
        setIsListening(false);
      };

      recognition.onerror = (error) => {
        console.error('Voice recognition error:', error);
        toast.error('Voice recognition failed');
        setIsListening(false);
      };

      recognition.start();
    } catch (error) {
      console.error('Voice recognition error:', error);
      toast.error('Voice recognition failed');
      setIsListening(false);
    }
  };

  const parseVoiceCommand = (transcript) => {
    const command = transcript.toLowerCase();

    if (command.includes('next') || command.includes('अगला')) {
      return 'next';
    } else if (command.includes('repeat') || command.includes('दोहराएं')) {
      return 'repeat';
    } else if (command.includes('pause') || command.includes('रोकें')) {
      return 'pause';
    }

    return null;
  };

  if (loading) {
    return (
      <Row className="justify-content-center">
        <Col md={6} className="text-center py-5">
          <div className="spinner-border text-primary mb-3" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <h4>Loading Recipe...</h4>
          <p className="text-muted">Please wait while we fetch the recipe details</p>
        </Col>
      </Row>
    );
  }

  if (!recipe) {
    return (
      <Row className="justify-content-center">
        <Col md={6} className="text-center py-5">
          <div className="text-muted mb-4">
            <i className="fas fa-exclamation-triangle fa-3x mb-3"></i>
            <h4>Recipe Not Found</h4>
            <p>The recipe you're looking for doesn't exist or has been removed.</p>
          </div>
          <Button as={Link} to="/" variant="primary">
            <i className="fas fa-home me-2"></i>
            Back to Home
          </Button>
        </Col>
      </Row>
    );
  }

  return (
    <>
      <Row>
        <Col lg={8}>
            {/* Recipe Header */}
            <Card className="mb-4">
              <Card.Img
                variant="top"
                src={recipe.image}
                alt={recipe.name}
                style={{ height: '300px', objectFit: 'cover' }}
              />
              <Card.Body>
                <div className="d-flex justify-content-between align-items-start mb-3">
                  <div className="flex-grow-1">
                    <Card.Title className="h2 mb-2">{recipe.name}</Card.Title>
                    <div className="d-flex align-items-center text-muted mb-2">
                      <img
                        src={recipe.createdBy?.image || '/default-avatar.png'}
                        alt="Chef"
                        className="rounded-circle me-2"
                        style={{ width: '24px', height: '24px', objectFit: 'cover' }}
                      />
                      <small>Created by {recipe.createdBy?.username || 'Unknown Chef'}</small>
                    </div>
                  </div>
                  {(canEditRecipe() || canDeleteRecipe()) && (
                    <Dropdown>
                      <Dropdown.Toggle variant="outline-secondary" size="sm">
                        <i className="fas fa-ellipsis-v"></i>
                      </Dropdown.Toggle>
                      <Dropdown.Menu>
                        {canEditRecipe() && (
                          <Dropdown.Item onClick={handleEditRecipe}>
                            <i className="fas fa-edit me-2"></i>
                            Edit Recipe
                          </Dropdown.Item>
                        )}
                        {canDeleteRecipe() && (
                          <Dropdown.Item onClick={handleDeleteRecipe} className="text-danger">
                            <i className="fas fa-trash me-2"></i>
                            Delete Recipe
                          </Dropdown.Item>
                        )}
                      </Dropdown.Menu>
                    </Dropdown>
                  )}
                </div>
                <Card.Text>{recipe.description}</Card.Text>
              </Card.Body>
            </Card>

            {/* Ingredients */}
            <Card className="mb-4">
              <Card.Header>
                <h4><i className="fas fa-list me-2"></i>Ingredients</h4>
              </Card.Header>
              <Card.Body>
                <ListGroup variant="flush">
                  {recipe.ingredients && recipe.ingredients.length > 0 ? (
                    recipe.ingredients.map((ingredient, index) => (
                      <ListGroup.Item key={index}>
                        <i className="fas fa-check-circle text-success me-2"></i>
                        {ingredient}
                      </ListGroup.Item>
                    ))
                  ) : (
                    <ListGroup.Item>
                      <i className="fas fa-info-circle text-muted me-2"></i>
                      No ingredients listed
                    </ListGroup.Item>
                  )}
                </ListGroup>
              </Card.Body>
            </Card>

            {/* Cooking Steps */}
            <Card className="mb-4">
              <Card.Header className="d-flex justify-content-between align-items-center">
                <h4><i className="fas fa-clipboard-list me-2"></i>Cooking Steps</h4>
                <Badge bg="primary">
                  Step {currentStep + 1} of {recipe.steps?.length || 0}
                </Badge>
              </Card.Header>
              <Card.Body>
                {recipe.steps && recipe.steps.length > 0 ? (
                  <>
                    <div className="current-step mb-4 p-3 bg-light rounded">
                      <h5>Step {currentStep + 1}</h5>
                      <p className="mb-0">{recipe.steps[currentStep]}</p>
                    </div>

                {/* Voice Controls */}
                <div className="voice-controls mb-4">
                  <h6><i className="fas fa-microphone me-2"></i>Voice Controls</h6>

                  {/* Enhanced Voice Cooking Mode for Users */}
                  {isUser() && (
                    <div className="mb-3">
                      <Button
                        variant="primary"
                        size="lg"
                        onClick={startVoiceCooking}
                        className="w-100 mb-2"
                      >
                        <i className="fas fa-magic me-2"></i>
                        Start Voice Cooking Mode
                      </Button>
                      <small className="text-muted d-block">
                        Full hands-free cooking with automatic timers and step guidance
                      </small>
                    </div>
                  )}

                  <div className="d-flex gap-2 flex-wrap">
                    <Button
                      variant="success"
                      onClick={handleRepeatStep}
                      disabled={isSpeaking}
                    >
                      {isSpeaking ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2"></span>
                          Speaking...
                        </>
                      ) : (
                        <>
                          <i className="fas fa-volume-up me-2"></i>
                          Repeat Step
                        </>
                      )}
                    </Button>

                    <Button
                      variant="info"
                      onClick={startVoiceListening}
                      disabled={isListening || isSpeaking}
                    >
                      {isListening ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2"></span>
                          Listening...
                        </>
                      ) : (
                        <>
                          <i className="fas fa-microphone me-2"></i>
                          Voice Command
                        </>
                      )}
                    </Button>
                  </div>
                  <small className="text-muted d-block mt-2">
                    Voice commands: "Next Step", "Repeat Step", "Pause"
                  </small>
                </div>

                {/* Step Navigation */}
                <div className="step-navigation">
                  <div className="d-flex justify-content-between">
                    <Button
                      variant="outline-secondary"
                      onClick={handlePreviousStep}
                      disabled={currentStep === 0 || !recipe.steps || recipe.steps.length === 0}
                    >
                      <i className="fas fa-arrow-left me-2"></i>
                      Previous Step
                    </Button>

                    <Button
                      variant="primary"
                      onClick={handleNextStep}
                      disabled={!recipe.steps || recipe.steps.length === 0 || currentStep === recipe.steps.length - 1}
                    >
                      Next Step
                      <i className="fas fa-arrow-right ms-2"></i>
                    </Button>
                  </div>
                </div>
                  </>
                ) : (
                  <div className="text-center py-4">
                    <i className="fas fa-info-circle text-muted fa-2x mb-3"></i>
                    <h5>No cooking steps available</h5>
                    <p className="text-muted">This recipe doesn't have any cooking steps yet.</p>
                  </div>
                )}
              </Card.Body>
            </Card>

            {/* Feedback Section */}
            <FeedbackSection recipeId={recipe._id} />
          </Col>

          <Col lg={4}>
            {/* Timer Component */}
            <div className="sticky-top" style={{ top: '20px' }}>
              <Timer />
            </div>
          </Col>
        </Row>

        {/* Edit Recipe Modal */}
        <RecipeModal
          show={showEditModal}
          onHide={handleModalClose}
          recipe={recipe}
        />

        {/* Voice Cooking Mode */}
        {showCookingMode && (
          <CookingMode
            recipe={recipe}
            onClose={closeCookingMode}
          />
        )}
      </>
    );
};

export default RecipeDetail;

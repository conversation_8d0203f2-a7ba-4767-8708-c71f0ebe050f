import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Container, Row, Col, Card, Button, Badge, ListGroup, Navbar, Nav } from 'react-bootstrap';
import { toast } from 'react-toastify';
import axiosInstance from '../utils/axiosConfig';
import { RECIPE_ROUTES } from '../utils/apiRoutes';
import { getUserData, clearUserData } from '../utils/auth';
import VoiceAssistant from '../utils/voiceAssistant';
import Timer from '../components/Timer';
import FeedbackSection from '../components/FeedbackSection';

const RecipeDetail = () => {
  const { id } = useParams();
  const [recipe, setRecipe] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [voiceAssistant] = useState(new VoiceAssistant());
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const userData = getUserData();

  useEffect(() => {
    fetchRecipe();
  }, [id]);

  const fetchRecipe = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(RECIPE_ROUTES.GET_BY_ID(id));
      if (response.data.success) {
        setRecipe(response.data.recipe);
      }
    } catch (error) {
      console.error('Error fetching recipe:', error);
      toast.error('Failed to fetch recipe details');
    } finally {
      setLoading(false);
    }
  };

  const speakStep = async (stepIndex) => {
    if (!recipe || !recipe.steps[stepIndex]) return;
    
    try {
      setIsSpeaking(true);
      const stepText = `Step ${stepIndex + 1}: ${recipe.steps[stepIndex]}`;
      await voiceAssistant.speak(stepText, 'hi-IN');
    } catch (error) {
      console.error('Error speaking step:', error);
      toast.error('Voice assistant not available');
    } finally {
      setIsSpeaking(false);
    }
  };

  const handleNextStep = () => {
    if (currentStep < recipe.steps.length - 1) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      speakStep(nextStep);
    } else {
      toast.info('You have completed all steps!');
    }
  };

  const handlePreviousStep = () => {
    if (currentStep > 0) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      speakStep(prevStep);
    }
  };

  const handleRepeatStep = () => {
    speakStep(currentStep);
  };

  const startVoiceListening = async () => {
    try {
      setIsListening(true);
      const transcript = await voiceAssistant.listen();
      const command = voiceAssistant.parseVoiceCommand(transcript);
      
      switch (command) {
        case 'next':
          handleNextStep();
          break;
        case 'repeat':
          handleRepeatStep();
          break;
        case 'pause':
          voiceAssistant.stopSpeaking();
          break;
        default:
          toast.info(`Voice command not recognized: ${transcript}`);
      }
    } catch (error) {
      console.error('Voice recognition error:', error);
      toast.error('Voice recognition failed');
    } finally {
      setIsListening(false);
    }
  };

  const handleLogout = () => {
    clearUserData();
    toast.success('Logged out successfully');
    window.location.href = '/';
  };

  if (loading) {
    return (
      <Container className="py-5 text-center">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-2">Loading recipe...</p>
      </Container>
    );
  }

  if (!recipe) {
    return (
      <Container className="py-5 text-center">
        <h3>Recipe not found</h3>
        <Link to="/" className="btn btn-primary">Back to Home</Link>
      </Container>
    );
  }

  return (
    <>
      {/* Navigation Bar */}
      <Navbar bg="primary" variant="dark" expand="lg" className="mb-4">
        <Container>
          <Navbar.Brand as={Link} to="/">
            <i className="fas fa-utensils me-2"></i>
            Cookify
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="ms-auto">
              <Nav.Link className="text-white">
                Welcome, {userData?.username}!
              </Nav.Link>
              <Button variant="outline-light" onClick={handleLogout}>
                Logout
              </Button>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>

      <Container>
        <Row>
          <Col lg={8}>
            {/* Recipe Header */}
            <Card className="mb-4">
              <Card.Img
                variant="top"
                src={recipe.image}
                alt={recipe.name}
                style={{ height: '300px', objectFit: 'cover' }}
              />
              <Card.Body>
                <Card.Title className="h2">{recipe.name}</Card.Title>
                <Card.Text>{recipe.description}</Card.Text>
              </Card.Body>
            </Card>

            {/* Ingredients */}
            <Card className="mb-4">
              <Card.Header>
                <h4><i className="fas fa-list me-2"></i>Ingredients</h4>
              </Card.Header>
              <Card.Body>
                <ListGroup variant="flush">
                  {recipe.ingredients.map((ingredient, index) => (
                    <ListGroup.Item key={index}>
                      <i className="fas fa-check-circle text-success me-2"></i>
                      {ingredient}
                    </ListGroup.Item>
                  ))}
                </ListGroup>
              </Card.Body>
            </Card>

            {/* Cooking Steps */}
            <Card className="mb-4">
              <Card.Header className="d-flex justify-content-between align-items-center">
                <h4><i className="fas fa-clipboard-list me-2"></i>Cooking Steps</h4>
                <Badge bg="primary">
                  Step {currentStep + 1} of {recipe.steps.length}
                </Badge>
              </Card.Header>
              <Card.Body>
                <div className="current-step mb-4 p-3 bg-light rounded">
                  <h5>Step {currentStep + 1}</h5>
                  <p className="mb-0">{recipe.steps[currentStep]}</p>
                </div>

                {/* Voice Controls */}
                <div className="voice-controls mb-4">
                  <h6><i className="fas fa-microphone me-2"></i>Voice Controls</h6>
                  <div className="d-flex gap-2 flex-wrap">
                    <Button
                      variant="success"
                      onClick={handleRepeatStep}
                      disabled={isSpeaking}
                    >
                      {isSpeaking ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2"></span>
                          Speaking...
                        </>
                      ) : (
                        <>
                          <i className="fas fa-volume-up me-2"></i>
                          Repeat Step
                        </>
                      )}
                    </Button>
                    
                    <Button
                      variant="info"
                      onClick={startVoiceListening}
                      disabled={isListening || isSpeaking}
                    >
                      {isListening ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2"></span>
                          Listening...
                        </>
                      ) : (
                        <>
                          <i className="fas fa-microphone me-2"></i>
                          Voice Command
                        </>
                      )}
                    </Button>
                  </div>
                  <small className="text-muted d-block mt-2">
                    Voice commands: "Next Step", "Repeat Step", "Pause"
                  </small>
                </div>

                {/* Step Navigation */}
                <div className="step-navigation">
                  <div className="d-flex justify-content-between">
                    <Button
                      variant="outline-secondary"
                      onClick={handlePreviousStep}
                      disabled={currentStep === 0}
                    >
                      <i className="fas fa-arrow-left me-2"></i>
                      Previous Step
                    </Button>
                    
                    <Button
                      variant="primary"
                      onClick={handleNextStep}
                      disabled={currentStep === recipe.steps.length - 1}
                    >
                      Next Step
                      <i className="fas fa-arrow-right ms-2"></i>
                    </Button>
                  </div>
                </div>
              </Card.Body>
            </Card>

            {/* Feedback Section */}
            <FeedbackSection recipeId={recipe._id} />
          </Col>

          <Col lg={4}>
            {/* Timer Component */}
            <div className="sticky-top" style={{ top: '20px' }}>
              <Timer />
            </div>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default RecipeDetail;

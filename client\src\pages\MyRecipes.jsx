import { useState, useEffect } from 'react';
import { Row, Col, Card, Button, Table, Badge, Form, InputGroup, Modal, Tab, Tabs } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import axiosInstance from '../utils/axiosConfig';
import { RECIPE_ROUTES, FEEDBACK_ROUTES } from '../utils/apiRoutes';
import { getUserData, isAdmin, isChef } from '../utils/auth';
import RecipeModal from '../components/RecipeModal';

const MyRecipes = () => {
  const [recipes, setRecipes] = useState([]);
  const [filteredRecipes, setFilteredRecipes] = useState([]);
  const [feedback, setFeedback] = useState([]);
  const [filteredFeedback, setFilteredFeedback] = useState([]);
  const [loading, setLoading] = useState(true);
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [feedbackSearchTerm, setFeedbackSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingRecipe, setEditingRecipe] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingRecipe, setDeletingRecipe] = useState(null);
  const [activeTab, setActiveTab] = useState('recipes');
  const userData = getUserData();

  useEffect(() => {
    fetchMyRecipes();
  }, []);

  useEffect(() => {
    if ((isChef() || isAdmin()) && recipes.length > 0) {
      fetchMyFeedback();
    }
  }, [recipes]);

  useEffect(() => {
    filterRecipes();
  }, [recipes, searchTerm]);

  useEffect(() => {
    filterFeedback();
  }, [feedback, feedbackSearchTerm]);

  const fetchMyRecipes = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(RECIPE_ROUTES.GET_ALL);
      if (response.data.success) {
        const allRecipes = response.data.recipes;
        const myRecipes = isAdmin()
          ? allRecipes
          : allRecipes.filter(recipe => recipe.createdBy?._id === userData.id);
        setRecipes(myRecipes);
      }
    } catch (error) {
      console.error('Error fetching recipes:', error);
      toast.error(isAdmin() ? 'Failed to fetch recipes' : 'Failed to fetch your recipes');
    } finally {
      setLoading(false);
    }
  };

  const fetchMyFeedback = async () => {
    try {
      setFeedbackLoading(true);
      const response = await axiosInstance.get(FEEDBACK_ROUTES.GET_ALL);
      if (response.data.success) {
        const allFeedbacks = response.data.feedbacks || [];

        const myRecipeIds = isAdmin()
          ? recipes.map(recipe => recipe._id)
          : recipes.filter(recipe => recipe.createdBy?._id === userData.id).map(recipe => recipe._id);

        const myFeedbacks = allFeedbacks.filter(fb => {
          if (typeof fb.recipe === 'string') {
            return myRecipeIds.includes(fb.recipe);
          } else if (fb.recipe && typeof fb.recipe === 'object') {
            return myRecipeIds.includes(fb.recipe._id);
          }
          return false;
        });

        setFeedback(myFeedbacks);
      }
    } catch (error) {
      console.error('Error fetching feedback:', error);
      toast.error('Failed to fetch feedback');
    } finally {
      setFeedbackLoading(false);
    }
  };

  const filterRecipes = () => {
    const filtered = recipes.filter(recipe =>
      recipe.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      recipe.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredRecipes(filtered);
  };

  const filterFeedback = () => {
    const filtered = feedback.filter(fb => {
      const recipeName = typeof fb.recipe === 'object' ? fb.recipe?.name || '' : '';
      const userName = fb.user?.username || '';
      const description = fb.description || '';

      return recipeName.toLowerCase().includes(feedbackSearchTerm.toLowerCase()) ||
             userName.toLowerCase().includes(feedbackSearchTerm.toLowerCase()) ||
             description.toLowerCase().includes(feedbackSearchTerm.toLowerCase());
    });
    setFilteredFeedback(filtered);
  };

  const handleDeleteRecipe = async () => {
    if (!deletingRecipe) return;

    try {
      const response = await axiosInstance.delete(RECIPE_ROUTES.DELETE(deletingRecipe._id));
      if (response.data.success) {
        toast.success('Recipe deleted successfully');
        fetchMyRecipes();
        setShowDeleteModal(false);
        setDeletingRecipe(null);
      }
    } catch (error) {
      console.error('Error deleting recipe:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete recipe';
      toast.error(errorMessage);
    }
  };

  const handleEditRecipe = (recipe) => {
    setEditingRecipe(recipe);
    setShowModal(true);
  };

  const handleAddRecipe = () => {
    setEditingRecipe(null);
    setShowModal(true);
  };

  const handleModalClose = () => {
    setShowModal(false);
    setEditingRecipe(null);
    fetchMyRecipes();
    if (isChef() || isAdmin()) {
      fetchMyFeedback();
    }
  };

  const confirmDelete = (recipe) => {
    setDeletingRecipe(recipe);
    setShowDeleteModal(true);
  };

  const renderStars = (rating) => {
    return (
      <div className="d-flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <i
            key={star}
            className={`fas fa-star ${star <= rating ? 'text-warning' : 'text-muted'}`}
            style={{ fontSize: '0.875rem' }}
          />
        ))}
      </div>
    );
  };

  const getAverageRating = () => {
    if (feedback.length === 0) return 0;
    const total = feedback.reduce((sum, fb) => sum + fb.rating, 0);
    return (total / feedback.length).toFixed(1);
  };

  return (
    <>
      <Row className="mb-4">
        <Col>
          <div className="d-flex align-items-center justify-content-between">
            <div>
              <h1 className="h3 mb-1">
                {isAdmin() ? 'All Recipes' : 'My Recipes'}
              </h1>
              <p className="text-muted mb-0">
                {isAdmin()
                  ? 'Manage all recipes and view feedback'
                  : 'Create and manage your own recipes'
                }
              </p>
            </div>
            <Button variant="primary" onClick={handleAddRecipe} size="lg">
              <i className="fas fa-plus me-2"></i>
              Create New Recipe
            </Button>
          </div>
        </Col>
      </Row>

      <Row className="mb-4">
        <Col lg={3} md={6}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <div className="text-primary mb-2">
                <i className="fas fa-utensils fa-2x"></i>
              </div>
              <h3 className="h4 mb-1">{recipes.length}</h3>
              <p className="text-muted mb-0">
                {isAdmin() ? 'Total Recipes' : 'My Recipes'}
              </p>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={3} md={6}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <div className="text-success mb-2">
                <i className="fas fa-clock fa-2x"></i>
              </div>
              <h3 className="h4 mb-1">
                {recipes.filter(r => {
                  const weekAgo = new Date();
                  weekAgo.setDate(weekAgo.getDate() - 7);
                  return new Date(r.createdAt) > weekAgo;
                }).length}
              </h3>
              <p className="text-muted mb-0">This Week</p>
            </Card.Body>
          </Card>
        </Col>
        {(isChef() || isAdmin()) && (
          <Col lg={3} md={6}>
            <Card className="border-0 shadow-sm h-100">
              <Card.Body className="text-center">
                <div className="text-info mb-2">
                  <i className="fas fa-comments fa-2x"></i>
                </div>
                <h3 className="h4 mb-1">{feedback.length}</h3>
                <p className="text-muted mb-0">Total Feedback</p>
              </Card.Body>
            </Card>
          </Col>
        )}
        {(isChef() || isAdmin()) && feedback.length > 0 && (
          <Col lg={3} md={6}>
            <Card className="border-0 shadow-sm h-100">
              <Card.Body className="text-center">
                <div className="text-warning mb-2">
                  <i className="fas fa-star fa-2x"></i>
                </div>
                <h3 className="h4 mb-1">{getAverageRating()}</h3>
                <p className="text-muted mb-0">Avg Rating</p>
              </Card.Body>
            </Card>
          </Col>
        )}
        <Col lg={(isChef() || isAdmin()) ? 12 : 6} className="mt-3 mt-lg-0">
          <InputGroup size="lg">
            <InputGroup.Text>
              <i className="fas fa-search"></i>
            </InputGroup.Text>
            <Form.Control
              type="text"
              placeholder="Search your recipes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
        </Col>
      </Row>

      {(isChef() || isAdmin()) ? (
        <Tabs
          activeKey={activeTab}
          onSelect={(k) => setActiveTab(k)}
          className="mb-4"
        >
          <Tab eventKey="recipes" title={<><i className="fas fa-utensils me-2"></i>Recipes ({recipes.length})</>}>
            {renderRecipesSection()}
          </Tab>
          <Tab eventKey="feedback" title={<><i className="fas fa-comments me-2"></i>Feedback ({feedback.length})</>}>
            {renderFeedbackSection()}
          </Tab>
        </Tabs>
      ) : (
        renderRecipesSection()
      )}
    </>
  );

  function renderRecipesSection() {
    return (
      <>
        {loading ? (
        <Row className="justify-content-center">
          <Col md={6} className="text-center py-5">
            <div className="spinner-border text-primary mb-3" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <h4>Loading Your Recipes...</h4>
            <p className="text-muted">Please wait while we fetch your recipes</p>
          </Col>
        </Row>
      ) : filteredRecipes.length > 0 ? (
        <>
          {/* Desktop Table View */}
          <Card className="border-0 shadow-sm d-none d-lg-block">
            <Card.Header className="bg-white border-bottom">
              <h5 className="mb-0">
                <i className="fas fa-list me-2"></i>
                {isAdmin() ? 'All Recipes' : 'Your Recipes'} ({filteredRecipes.length})
              </h5>
            </Card.Header>
            <Card.Body>
              <Table responsive hover className="mb-0">
                <thead>
                  <tr>
                    <th>Recipe</th>
                    <th>Ingredients</th>
                    <th>Steps</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredRecipes.map((recipe) => (
                    <tr key={recipe._id}>
                      <td>
                        <div className="d-flex align-items-center">
                          <img
                            src={recipe.image}
                            alt={recipe.name}
                            className="rounded me-3"
                            style={{ width: '60px', height: '60px', objectFit: 'cover' }}
                          />
                          <div>
                            <div className="fw-semibold">{recipe.name}</div>
                            <small className="text-muted">
                              {recipe.description.length > 60
                                ? `${recipe.description.substring(0, 60)}...`
                                : recipe.description}
                            </small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <Badge bg="success">{recipe.ingredients?.length || 0}</Badge>
                      </td>
                      <td>
                        <Badge bg="primary">{recipe.steps?.length || 0}</Badge>
                      </td>
                      <td>
                        <small className="text-muted">
                          {new Date(recipe.createdAt).toLocaleDateString()}
                        </small>
                      </td>
                      <td>
                        <div className="d-flex gap-2">
                          <Button
                            as={Link}
                            to={`/recipe/${recipe._id}`}
                            variant="outline-info"
                            size="sm"
                            title="View Recipe"
                          >
                            <i className="fas fa-eye"></i>
                          </Button>
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => handleEditRecipe(recipe)}
                            title="Edit Recipe"
                          >
                            <i className="fas fa-edit"></i>
                          </Button>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => confirmDelete(recipe)}
                            title="Delete Recipe"
                          >
                            <i className="fas fa-trash"></i>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>

          {/* Mobile Card View */}
          <Row className="d-lg-none">
            {filteredRecipes.map((recipe) => (
              <Col md={6} className="mb-4" key={recipe._id}>
                <Card className="border-0 shadow-sm h-100">
                  <Card.Img
                    variant="top"
                    src={recipe.image}
                    alt={recipe.name}
                    style={{ height: '200px', objectFit: 'cover' }}
                  />
                  <Card.Body className="d-flex flex-column">
                    <Card.Title className="h6">{recipe.name}</Card.Title>
                    <Card.Text className="text-muted flex-grow-1 small">
                      {recipe.description.length > 100
                        ? `${recipe.description.substring(0, 100)}...`
                        : recipe.description}
                    </Card.Text>
                    <div className="d-flex justify-content-between align-items-center mb-3">
                      <div>
                        <Badge bg="success" className="me-2">
                          {recipe.ingredients?.length || 0} ingredients
                        </Badge>
                        <Badge bg="primary">
                          {recipe.steps?.length || 0} steps
                        </Badge>
                      </div>
                    </div>
                    <div className="d-flex gap-2">
                      <Button
                        as={Link}
                        to={`/recipe/${recipe._id}`}
                        variant="outline-info"
                        size="sm"
                        className="flex-grow-1"
                      >
                        <i className="fas fa-eye me-1"></i>
                        View
                      </Button>
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={() => handleEditRecipe(recipe)}
                      >
                        <i className="fas fa-edit"></i>
                      </Button>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => confirmDelete(recipe)}
                      >
                        <i className="fas fa-trash"></i>
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </>
      ) : (
        <Card className="border-0 shadow-sm">
          <Card.Body>
            <div className="text-center py-5">
              <div className="text-muted">
                {searchTerm ? (
                  <>
                    <i className="fas fa-search fa-3x mb-3"></i>
                    <h4>No recipes found</h4>
                    <p>Try adjusting your search terms.</p>
                    <Button
                      variant="primary"
                      onClick={() => setSearchTerm('')}
                    >
                      Clear Search
                    </Button>
                  </>
                ) : (
                  <>
                    <i className="fas fa-utensils fa-3x mb-3"></i>
                    <h4>No recipes yet</h4>
                    <p>Start creating your first recipe to share with the world!</p>
                    <Button variant="primary" onClick={handleAddRecipe} size="lg">
                      <i className="fas fa-plus me-2"></i>
                      Create Your First Recipe
                    </Button>
                  </>
                )}
              </div>
            </div>
          </Card.Body>
        </Card>
      )}
      </>
    );
  }

  function renderFeedbackSection() {
    return (
      <>
        <Row className="mb-4">
          <Col md={8}>
            <InputGroup size="lg">
              <InputGroup.Text>
                <i className="fas fa-search"></i>
              </InputGroup.Text>
              <Form.Control
                type="text"
                placeholder="Search feedback by recipe name, user, or content..."
                value={feedbackSearchTerm}
                onChange={(e) => setFeedbackSearchTerm(e.target.value)}
              />
            </InputGroup>
          </Col>
          <Col md={4}>
            <Card className="border-0 shadow-sm h-100">
              <Card.Body className="text-center">
                <h4 className="text-warning mb-1">{getAverageRating()}</h4>
                <p className="text-muted mb-0 small">Average Rating</p>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        <Card className="border-0 shadow-sm">
          <Card.Header className="bg-white border-bottom">
            <h5 className="mb-0">
              <i className="fas fa-comments me-2"></i>
              Feedback for Your Recipes ({filteredFeedback.length})
            </h5>
          </Card.Header>
          <Card.Body>
            {feedbackLoading ? (
              <div className="text-center py-5">
                <div className="spinner-border text-primary mb-3" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
                <p className="text-muted">Loading feedback...</p>
              </div>
            ) : filteredFeedback.length > 0 ? (
              <Table responsive hover className="mb-0">
                <thead>
                  <tr>
                    <th>Recipe</th>
                    <th>User</th>
                    <th>Rating</th>
                    <th>Feedback</th>
                    <th>Date</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredFeedback.map((fb) => (
                    <tr key={fb._id}>
                      <td>
                        <div className="d-flex align-items-center">
                          {fb.recipe?.image && (
                            <img
                              src={fb.recipe.image}
                              alt={fb.recipe.name}
                              className="rounded me-2"
                              style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                            />
                          )}
                          <div>
                            <div className="fw-semibold">{fb.recipe?.name || 'Unknown Recipe'}</div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <Badge bg="secondary">
                          {fb.user?.username || 'Anonymous'}
                        </Badge>
                      </td>
                      <td>
                        <div className="d-flex align-items-center">
                          {renderStars(fb.rating)}
                          <span className="ms-2 small">{fb.rating}/5</span>
                        </div>
                      </td>
                      <td>
                        <div style={{ maxWidth: '300px' }}>
                          {fb.description.length > 100
                            ? `${fb.description.substring(0, 100)}...`
                            : fb.description}
                        </div>
                      </td>
                      <td>
                        <small className="text-muted">
                          {new Date(fb.createdAt).toLocaleDateString()}
                        </small>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            ) : (
              <div className="text-center py-5">
                <div className="text-muted">
                  {feedbackSearchTerm ? (
                    <>
                      <i className="fas fa-search fa-3x mb-3"></i>
                      <h4>No feedback found</h4>
                      <p>Try adjusting your search criteria.</p>
                      <Button
                        variant="primary"
                        onClick={() => setFeedbackSearchTerm('')}
                      >
                        Clear Search
                      </Button>
                    </>
                  ) : (
                    <>
                      <i className="fas fa-comments fa-3x mb-3"></i>
                      <h4>No feedback yet</h4>
                      <p>Feedback will appear here once users start reviewing your recipes.</p>
                    </>
                  )}
                </div>
              </div>
            )}
          </Card.Body>
        </Card>
      </>
    );
  }

      <RecipeModal
        show={showModal}
        onHide={handleModalClose}
        recipe={editingRecipe}
      />

      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <i className="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5>Are you sure?</h5>
            <p className="text-muted">
              This will permanently delete "{deletingRecipe?.name}". This action cannot be undone.
            </p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDeleteRecipe}>
            <i className="fas fa-trash me-2"></i>
            Delete Recipe
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default MyRecipes;

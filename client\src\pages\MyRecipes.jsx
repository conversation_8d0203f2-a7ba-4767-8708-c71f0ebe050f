import { useState, useEffect } from 'react';
import { Row, Col, Card, Button, Table, Badge, Form, InputGroup, Modal } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import axiosInstance from '../utils/axiosConfig';
import { RECIPE_ROUTES } from '../utils/apiRoutes';
import { getUserData, isAdmin } from '../utils/auth';
import RecipeModal from '../components/RecipeModal';

const MyRecipes = () => {
  const [recipes, setRecipes] = useState([]);
  const [filteredRecipes, setFilteredRecipes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingRecipe, setEditingRecipe] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingRecipe, setDeletingRecipe] = useState(null);
  const userData = getUserData();

  useEffect(() => {
    fetchMyRecipes();
  }, []);

  useEffect(() => {
    filterRecipes();
  }, [recipes, searchTerm]);

  const fetchMyRecipes = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(RECIPE_ROUTES.GET_ALL);
      if (response.data.success) {
        // Filter recipes created by current user (unless admin)
        const allRecipes = response.data.recipes;
        const myRecipes = isAdmin() 
          ? allRecipes 
          : allRecipes.filter(recipe => recipe.createdBy?._id === userData.id);
        setRecipes(myRecipes);
      }
    } catch (error) {
      console.error('Error fetching recipes:', error);
      toast.error('Failed to fetch your recipes');
    } finally {
      setLoading(false);
    }
  };

  const filterRecipes = () => {
    const filtered = recipes.filter(recipe =>
      recipe.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      recipe.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredRecipes(filtered);
  };

  const handleDeleteRecipe = async () => {
    if (!deletingRecipe) return;

    try {
      const response = await axiosInstance.delete(RECIPE_ROUTES.DELETE(deletingRecipe._id));
      if (response.data.success) {
        toast.success('Recipe deleted successfully');
        fetchMyRecipes();
        setShowDeleteModal(false);
        setDeletingRecipe(null);
      }
    } catch (error) {
      console.error('Error deleting recipe:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete recipe';
      toast.error(errorMessage);
    }
  };

  const handleEditRecipe = (recipe) => {
    setEditingRecipe(recipe);
    setShowModal(true);
  };

  const handleAddRecipe = () => {
    setEditingRecipe(null);
    setShowModal(true);
  };

  const handleModalClose = () => {
    setShowModal(false);
    setEditingRecipe(null);
    fetchMyRecipes();
  };

  const confirmDelete = (recipe) => {
    setDeletingRecipe(recipe);
    setShowDeleteModal(true);
  };

  return (
    <>
      {/* Page Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex align-items-center justify-content-between">
            <div>
              <h1 className="h3 mb-1">My Recipes</h1>
              <p className="text-muted mb-0">
                Create and manage your own recipes
              </p>
            </div>
            <Button variant="primary" onClick={handleAddRecipe} size="lg">
              <i className="fas fa-plus me-2"></i>
              Create New Recipe
            </Button>
          </div>
        </Col>
      </Row>

      {/* Stats and Search */}
      <Row className="mb-4">
        <Col lg={3} md={6}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <div className="text-primary mb-2">
                <i className="fas fa-utensils fa-2x"></i>
              </div>
              <h3 className="h4 mb-1">{recipes.length}</h3>
              <p className="text-muted mb-0">Total Recipes</p>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={3} md={6}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <div className="text-success mb-2">
                <i className="fas fa-clock fa-2x"></i>
              </div>
              <h3 className="h4 mb-1">
                {recipes.filter(r => {
                  const weekAgo = new Date();
                  weekAgo.setDate(weekAgo.getDate() - 7);
                  return new Date(r.createdAt) > weekAgo;
                }).length}
              </h3>
              <p className="text-muted mb-0">This Week</p>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={6}>
          <InputGroup size="lg">
            <InputGroup.Text>
              <i className="fas fa-search"></i>
            </InputGroup.Text>
            <Form.Control
              type="text"
              placeholder="Search your recipes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
        </Col>
      </Row>

      {/* Recipes Grid/Table */}
      {loading ? (
        <Row className="justify-content-center">
          <Col md={6} className="text-center py-5">
            <div className="spinner-border text-primary mb-3" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <h4>Loading Your Recipes...</h4>
            <p className="text-muted">Please wait while we fetch your recipes</p>
          </Col>
        </Row>
      ) : filteredRecipes.length > 0 ? (
        <>
          {/* Desktop Table View */}
          <Card className="border-0 shadow-sm d-none d-lg-block">
            <Card.Header className="bg-white border-bottom">
              <h5 className="mb-0">
                <i className="fas fa-list me-2"></i>
                Your Recipes ({filteredRecipes.length})
              </h5>
            </Card.Header>
            <Card.Body>
              <Table responsive hover className="mb-0">
                <thead>
                  <tr>
                    <th>Recipe</th>
                    <th>Ingredients</th>
                    <th>Steps</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredRecipes.map((recipe) => (
                    <tr key={recipe._id}>
                      <td>
                        <div className="d-flex align-items-center">
                          <img
                            src={recipe.image}
                            alt={recipe.name}
                            className="rounded me-3"
                            style={{ width: '60px', height: '60px', objectFit: 'cover' }}
                          />
                          <div>
                            <div className="fw-semibold">{recipe.name}</div>
                            <small className="text-muted">
                              {recipe.description.length > 60
                                ? `${recipe.description.substring(0, 60)}...`
                                : recipe.description}
                            </small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <Badge bg="success">{recipe.ingredients?.length || 0}</Badge>
                      </td>
                      <td>
                        <Badge bg="primary">{recipe.steps?.length || 0}</Badge>
                      </td>
                      <td>
                        <small className="text-muted">
                          {new Date(recipe.createdAt).toLocaleDateString()}
                        </small>
                      </td>
                      <td>
                        <div className="d-flex gap-2">
                          <Button
                            as={Link}
                            to={`/recipe/${recipe._id}`}
                            variant="outline-info"
                            size="sm"
                            title="View Recipe"
                          >
                            <i className="fas fa-eye"></i>
                          </Button>
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => handleEditRecipe(recipe)}
                            title="Edit Recipe"
                          >
                            <i className="fas fa-edit"></i>
                          </Button>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => confirmDelete(recipe)}
                            title="Delete Recipe"
                          >
                            <i className="fas fa-trash"></i>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>

          {/* Mobile Card View */}
          <Row className="d-lg-none">
            {filteredRecipes.map((recipe) => (
              <Col md={6} className="mb-4" key={recipe._id}>
                <Card className="border-0 shadow-sm h-100">
                  <Card.Img
                    variant="top"
                    src={recipe.image}
                    alt={recipe.name}
                    style={{ height: '200px', objectFit: 'cover' }}
                  />
                  <Card.Body className="d-flex flex-column">
                    <Card.Title className="h6">{recipe.name}</Card.Title>
                    <Card.Text className="text-muted flex-grow-1 small">
                      {recipe.description.length > 100
                        ? `${recipe.description.substring(0, 100)}...`
                        : recipe.description}
                    </Card.Text>
                    <div className="d-flex justify-content-between align-items-center mb-3">
                      <div>
                        <Badge bg="success" className="me-2">
                          {recipe.ingredients?.length || 0} ingredients
                        </Badge>
                        <Badge bg="primary">
                          {recipe.steps?.length || 0} steps
                        </Badge>
                      </div>
                    </div>
                    <div className="d-flex gap-2">
                      <Button
                        as={Link}
                        to={`/recipe/${recipe._id}`}
                        variant="outline-info"
                        size="sm"
                        className="flex-grow-1"
                      >
                        <i className="fas fa-eye me-1"></i>
                        View
                      </Button>
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={() => handleEditRecipe(recipe)}
                      >
                        <i className="fas fa-edit"></i>
                      </Button>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => confirmDelete(recipe)}
                      >
                        <i className="fas fa-trash"></i>
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </>
      ) : (
        <Card className="border-0 shadow-sm">
          <Card.Body>
            <div className="text-center py-5">
              <div className="text-muted">
                {searchTerm ? (
                  <>
                    <i className="fas fa-search fa-3x mb-3"></i>
                    <h4>No recipes found</h4>
                    <p>Try adjusting your search terms.</p>
                    <Button
                      variant="primary"
                      onClick={() => setSearchTerm('')}
                    >
                      Clear Search
                    </Button>
                  </>
                ) : (
                  <>
                    <i className="fas fa-utensils fa-3x mb-3"></i>
                    <h4>No recipes yet</h4>
                    <p>Start creating your first recipe to share with the world!</p>
                    <Button variant="primary" onClick={handleAddRecipe} size="lg">
                      <i className="fas fa-plus me-2"></i>
                      Create Your First Recipe
                    </Button>
                  </>
                )}
              </div>
            </div>
          </Card.Body>
        </Card>
      )}

      {/* Recipe Modal */}
      <RecipeModal
        show={showModal}
        onHide={handleModalClose}
        recipe={editingRecipe}
      />

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <i className="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5>Are you sure?</h5>
            <p className="text-muted">
              This will permanently delete "{deletingRecipe?.name}". This action cannot be undone.
            </p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDeleteRecipe}>
            <i className="fas fa-trash me-2"></i>
            Delete Recipe
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default MyRecipes;

import { useState, useEffect } from 'react';
import { Row, Col, Card, Button, Table, Badge, Form, InputGroup, Modal } from 'react-bootstrap';
import { toast } from 'react-toastify';
import axiosInstance from '../utils/axiosConfig';
import { RECIPE_ROUTES } from '../utils/apiRoutes';
import { getUserData, isAdmin, isChef } from '../utils/auth';
import RecipeModal from '../components/RecipeModal';

const RecipeManagement = () => {
  const [recipes, setRecipes] = useState([]);
  const [filteredRecipes, setFilteredRecipes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingRecipe, setEditingRecipe] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingRecipe, setDeletingRecipe] = useState(null);
  const userData = getUserData();

  useEffect(() => {
    fetchRecipes();
  }, []);

  useEffect(() => {
    filterRecipes();
  }, [recipes, searchTerm]);

  const fetchRecipes = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(RECIPE_ROUTES.GET_ALL);
      if (response.data.success) {
        setRecipes(response.data.recipes);
      }
    } catch (error) {
      console.error('Error fetching recipes:', error);
      toast.error('Failed to fetch recipes');
    } finally {
      setLoading(false);
    }
  };

  const filterRecipes = () => {
    const filtered = recipes.filter(recipe =>
      recipe.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      recipe.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (recipe.createdBy?.username || '').toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredRecipes(filtered);
  };

  const handleDeleteRecipe = async () => {
    if (!deletingRecipe) return;

    try {
      const response = await axiosInstance.delete(RECIPE_ROUTES.DELETE(deletingRecipe._id));
      if (response.data.success) {
        toast.success('Recipe deleted successfully');
        fetchRecipes();
        setShowDeleteModal(false);
        setDeletingRecipe(null);
      }
    } catch (error) {
      console.error('Error deleting recipe:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete recipe';
      toast.error(errorMessage);
    }
  };

  const handleEditRecipe = (recipe) => {
    setEditingRecipe(recipe);
    setShowModal(true);
  };

  const handleAddRecipe = () => {
    setEditingRecipe(null);
    setShowModal(true);
  };

  const handleModalClose = () => {
    setShowModal(false);
    setEditingRecipe(null);
    fetchRecipes();
  };

  const canDeleteRecipe = (recipe) => {
    return isAdmin() || (isChef() && recipe.createdBy?._id === userData.id);
  };

  const canEditRecipe = (recipe) => {
    return isAdmin() || (isChef() && recipe.createdBy?._id === userData.id);
  };

  const confirmDelete = (recipe) => {
    setDeletingRecipe(recipe);
    setShowDeleteModal(true);
  };

  return (
    <>
      {/* Page Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex align-items-center justify-content-between">
            <div>
              <h1 className="h3 mb-1">Recipe Management</h1>
              <p className="text-muted mb-0">
                Create, edit, and manage your recipes
              </p>
            </div>
            <Button variant="primary" onClick={handleAddRecipe}>
              <i className="fas fa-plus me-2"></i>
              Add New Recipe
            </Button>
          </div>
        </Col>
      </Row>

      {/* Search and Stats */}
      <Row className="mb-4">
        <Col md={8}>
          <InputGroup size="lg">
            <InputGroup.Text>
              <i className="fas fa-search"></i>
            </InputGroup.Text>
            <Form.Control
              type="text"
              placeholder="Search recipes by name, description, or chef..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
        </Col>
        <Col md={4}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <h4 className="text-primary mb-1">{filteredRecipes.length}</h4>
              <p className="text-muted mb-0 small">
                {searchTerm ? 'Filtered' : 'Total'} Recipes
              </p>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Recipes Table */}
      <Card className="border-0 shadow-sm">
        <Card.Header className="bg-white border-bottom">
          <h5 className="mb-0">
            <i className="fas fa-utensils me-2"></i>
            All Recipes
          </h5>
        </Card.Header>
        <Card.Body>
          {loading ? (
            <div className="text-center py-5">
              <div className="spinner-border text-primary mb-3" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="text-muted">Loading recipes...</p>
            </div>
          ) : filteredRecipes.length > 0 ? (
            <Table responsive hover className="mb-0">
              <thead>
                <tr>
                  <th>Recipe</th>
                  <th>Chef</th>
                  <th>Ingredients</th>
                  <th>Steps</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredRecipes.map((recipe) => (
                  <tr key={recipe._id}>
                    <td>
                      <div className="d-flex align-items-center">
                        <img
                          src={recipe.image}
                          alt={recipe.name}
                          className="rounded me-3"
                          style={{ width: '60px', height: '60px', objectFit: 'cover' }}
                        />
                        <div>
                          <div className="fw-semibold">{recipe.name}</div>
                          <small className="text-muted">
                            {recipe.description.length > 60
                              ? `${recipe.description.substring(0, 60)}...`
                              : recipe.description}
                          </small>
                        </div>
                      </div>
                    </td>
                    <td>
                      <Badge bg="secondary">
                        {recipe.createdBy?.username || 'Unknown'}
                      </Badge>
                    </td>
                    <td>
                      <Badge bg="success">{recipe.ingredients?.length || 0}</Badge>
                    </td>
                    <td>
                      <Badge bg="primary">{recipe.steps?.length || 0}</Badge>
                    </td>
                    <td>
                      <small className="text-muted">
                        {new Date(recipe.createdAt).toLocaleDateString()}
                      </small>
                    </td>
                    <td>
                      <div className="d-flex gap-2">
                        {canEditRecipe(recipe) && (
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => handleEditRecipe(recipe)}
                            title="Edit Recipe"
                          >
                            <i className="fas fa-edit"></i>
                          </Button>
                        )}
                        {canDeleteRecipe(recipe) && (
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => confirmDelete(recipe)}
                            title="Delete Recipe"
                          >
                            <i className="fas fa-trash"></i>
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          ) : (
            <div className="text-center py-5">
              <div className="text-muted">
                {searchTerm ? (
                  <>
                    <i className="fas fa-search fa-3x mb-3"></i>
                    <h4>No recipes found</h4>
                    <p>Try adjusting your search terms.</p>
                    <Button
                      variant="primary"
                      onClick={() => setSearchTerm('')}
                    >
                      Clear Search
                    </Button>
                  </>
                ) : (
                  <>
                    <i className="fas fa-utensils fa-3x mb-3"></i>
                    <h4>No recipes yet</h4>
                    <p>Start by creating your first recipe!</p>
                    <Button variant="primary" onClick={handleAddRecipe}>
                      <i className="fas fa-plus me-2"></i>
                      Add Recipe
                    </Button>
                  </>
                )}
              </div>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Recipe Modal */}
      <RecipeModal
        show={showModal}
        onHide={handleModalClose}
        recipe={editingRecipe}
      />

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <i className="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5>Are you sure?</h5>
            <p className="text-muted">
              This will permanently delete "{deletingRecipe?.name}". This action cannot be undone.
            </p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDeleteRecipe}>
            <i className="fas fa-trash me-2"></i>
            Delete Recipe
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default RecipeManagement;

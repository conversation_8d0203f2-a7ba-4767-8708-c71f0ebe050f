{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcrypt": "^6.0.0", "cloudinary": "^2.7.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-fileupload": "^1.5.2", "google-translate-tts": "^0.4.0-dev", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.2"}}
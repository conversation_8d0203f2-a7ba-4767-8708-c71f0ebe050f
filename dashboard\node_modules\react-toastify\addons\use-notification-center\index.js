"use client";
var u=Object.defineProperty;var m=Object.getOwnPropertyDescriptor;var C=Object.getOwnPropertyNames;var N=Object.prototype.hasOwnProperty;var A=(a,i)=>{for(var d in i)u(a,d,{get:i[d],enumerable:!0})},x=(a,i,d,c)=>{if(i&&typeof i=="object"||typeof i=="function")for(let o of C(i))!N.call(a,o)&&o!==d&&u(a,o,{get:()=>i[o],enumerable:!(c=m(i,o))||c.enumerable});return a};var p=a=>x(u({},"__esModule",{value:!0}),a);var y={};A(y,{decorate:()=>l,useNotificationCenter:()=>b});module.exports=p(y);var s=require("react"),I=require("react-toastify");function b(a={}){let i=(0,s.useRef)(a.sort||k),d=(0,s.useRef)(a.filter||null),[c,o]=(0,s.useState)(()=>a.data?d.current?a.data.filter(d.current).sort(i.current):[...a.data].sort(i.current):[]);return(0,s.useEffect)(()=>I.toast.onChange(t=>{if(t.status==="added"||t.status==="updated"){let e=l(t);if(d.current&&!d.current(e))return;o(r=>{let n=[],f=r.findIndex(D=>D.id===e.id);return f!==-1?(n=r.slice(),Object.assign(n[f],e,{createdAt:Date.now()})):r.length===0?n=[e]:n=[e,...r],n.sort(i.current)})}}),[]),{notifications:c,clear:()=>{o([])},markAllAsRead:(t=!0)=>{o(e=>e.map(r=>(r.read=t,r)))},markAsRead:(t,e=!0)=>{let r=n=>(n.id===t&&(n.read=e),n);Array.isArray(t)&&(r=n=>(t.includes(n.id)&&(n.read=e),n)),o(n=>n.map(r))},add:t=>{if(c.find(r=>r.id===t.id))return null;let e=l(t);return o(r=>[...r,e].sort(i.current)),e.id},update:(t,e)=>{let r=c.findIndex(n=>n.id===t);return r!==-1?(o(n=>{let f=[...n];return Object.assign(f[r],e,{createdAt:e.createdAt||Date.now()}),f.sort(i.current)}),e.id):null},remove:t=>{o(e=>e.filter(Array.isArray(t)?r=>!t.includes(r.id):r=>r.id!==t))},find:t=>Array.isArray(t)?c.filter(e=>t.includes(e.id)):c.find(e=>e.id===t),sort:t=>{i.current=t,o(e=>e.slice().sort(t))},get unreadCount(){return c.reduce((t,e)=>e.read?t:t+1,0)}}}function l(a){return a.id==null&&(a.id=Date.now().toString(36).substring(2,9)),a.createdAt||(a.createdAt=Date.now()),a.read==null&&(a.read=!1),a}function k(a,i){return i.createdAt-a.createdAt}0&&(module.exports={decorate,useNotificationCenter});
//# sourceMappingURL=index.js.map
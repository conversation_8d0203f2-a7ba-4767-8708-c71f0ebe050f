import { useState } from 'react';
import { <PERSON>, Col, Card, Form, Button, Alert, Tab, Tabs } from 'react-bootstrap';
import { getUserData, setUserData, isUser } from '../utils/auth';
import { toast } from 'react-toastify';
import SavedRecipes from '../components/SavedRecipes';

const Profile = () => {
  const userData = getUserData();
  const [formData, setFormData] = useState({
    username: userData?.username || '',
    email: userData?.email || '',
    phone: userData?.phone || '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.username || !formData.email || !formData.phone) {
      setError('All fields are required');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      // Note: This would need a backend endpoint for updating profile
      // For now, just update local storage
      const updatedUser = { ...userData, ...formData };
      setUserData(updatedUser);
      toast.success('Profile updated successfully!');
      
    } catch (error) {
      console.error('Profile update error:', error);
      setError('Failed to update profile');
      toast.error('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {/* Page Header */}
      <Row className="mb-4">
        <Col>
          <h1 className="h3 mb-1">Profile Settings</h1>
          <p className="text-muted mb-0">
            Manage your account information and preferences
          </p>
        </Col>
      </Row>

      <Row>
        <Col lg={8}>
          {/* Profile Information Card */}
          <Card className="border-0 shadow-sm mb-4">
            <Card.Header className="bg-white border-bottom">
              <h5 className="mb-0">
                <i className="fas fa-user me-2"></i>
                Personal Information
              </h5>
            </Card.Header>
            <Card.Body>
              {error && (
                <Alert variant="danger" className="mb-3">
                  {error}
                </Alert>
              )}

              <Form onSubmit={handleSubmit}>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Username</Form.Label>
                      <Form.Control
                        type="text"
                        name="username"
                        value={formData.username}
                        onChange={handleChange}
                        placeholder="Enter your username"
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Email Address</Form.Label>
                      <Form.Control
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="Enter your email"
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Phone Number</Form.Label>
                      <Form.Control
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        placeholder="Enter your phone number"
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Role</Form.Label>
                      <Form.Control
                        type="text"
                        value={userData?.role || 'User'}
                        disabled
                        className="bg-light"
                      />
                      <Form.Text className="text-muted">
                        Role cannot be changed
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                <div className="d-flex gap-2">
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2"></span>
                        Updating...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-save me-2"></i>
                        Update Profile
                      </>
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="outline-secondary"
                    onClick={() => {
                      setFormData({
                        username: userData?.username || '',
                        email: userData?.email || '',
                        phone: userData?.phone || '',
                      });
                      setError('');
                    }}
                  >
                    Reset
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>

          {/* Account Security Card */}
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-white border-bottom">
              <h5 className="mb-0">
                <i className="fas fa-shield-alt me-2"></i>
                Account Security
              </h5>
            </Card.Header>
            <Card.Body>
              <div className="d-flex align-items-center justify-content-between p-3 bg-light rounded">
                <div>
                  <h6 className="mb-1">Password</h6>
                  <p className="text-muted mb-0 small">
                    Last updated: Never
                  </p>
                </div>
                <Button variant="outline-primary" size="sm">
                  Change Password
                </Button>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          {/* Profile Picture Card */}
          <Card className="border-0 shadow-sm mb-4">
            <Card.Header className="bg-white border-bottom">
              <h5 className="mb-0">
                <i className="fas fa-camera me-2"></i>
                Profile Picture
              </h5>
            </Card.Header>
            <Card.Body className="text-center">
              <img
                src={userData?.image || '/default-avatar.png'}
                alt="Profile"
                className="rounded-circle mb-3"
                style={{ width: '120px', height: '120px', objectFit: 'cover' }}
              />
              <div>
                <Button variant="outline-primary" size="sm" className="mb-2">
                  <i className="fas fa-upload me-2"></i>
                  Upload New Photo
                </Button>
                <p className="text-muted small mb-0">
                  JPG, PNG or GIF. Max size 2MB.
                </p>
              </div>
            </Card.Body>
          </Card>

          {/* Account Stats Card */}
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-white border-bottom">
              <h5 className="mb-0">
                <i className="fas fa-chart-bar me-2"></i>
                Account Stats
              </h5>
            </Card.Header>
            <Card.Body>
              <div className="d-flex align-items-center justify-content-between mb-3">
                <span className="text-muted">Member Since</span>
                <span className="fw-semibold">
                  {userData?.createdAt 
                    ? new Date(userData.createdAt).toLocaleDateString()
                    : 'Unknown'
                  }
                </span>
              </div>
              <div className="d-flex align-items-center justify-content-between mb-3">
                <span className="text-muted">Recipes Viewed</span>
                <span className="fw-semibold">0</span>
              </div>
              <div className="d-flex align-items-center justify-content-between">
                <span className="text-muted">Feedback Given</span>
                <span className="fw-semibold">0</span>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Saved Cooking Sessions for Users */}
      {isUser() && (
        <Row className="mt-4">
          <Col>
            <SavedRecipes />
          </Col>
        </Row>
      )}
    </>
  );
};

export default Profile;

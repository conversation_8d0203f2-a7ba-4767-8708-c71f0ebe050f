import { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>, Badge, Alert, Modal } from 'react-bootstrap';
import { toast } from 'react-toastify';
import { getUserData } from '../utils/auth';

const CookingMode = ({ recipe, onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [cookingStarted, setCookingStarted] = useState(false);
  const [timers, setTimers] = useState({});
  const [completedSteps, setCompletedSteps] = useState([]);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [cookingNotes, setCookingNotes] = useState('');
  
  const recognitionRef = useRef(null);
  const synthRef = useRef(null);
  const userData = getUserData();

  useEffect(() => {
    initializeSpeechAPIs();
    startListening();
    
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (synthRef.current) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  const initializeSpeechAPIs = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onresult = handleVoiceCommand;
      recognitionRef.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        if (event.error !== 'no-speech') {
          toast.error('Voice recognition error. Please try again.');
        }
      };
    }

    synthRef.current = window.speechSynthesis;
  };

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      setIsListening(true);
      recognitionRef.current.start();
      speak("Welcome to cooking mode! Say 'start' to begin cooking, or 'help' for commands.");
    }
  };

  const speak = (text, callback = null) => {
    if (synthRef.current) {
      setIsSpeaking(true);
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = 'en-US'; // Set to English
      utterance.rate = 0.9;
      utterance.pitch = 1;
      utterance.volume = 0.8;

      utterance.onend = () => {
        setIsSpeaking(false);
        if (callback) callback();
      };

      utterance.onerror = () => {
        setIsSpeaking(false);
        console.error('Speech synthesis error');
      };

      synthRef.current.speak(utterance);
    }
  };

  const handleVoiceCommand = (event) => {
    const transcript = event.results[event.results.length - 1][0].transcript.toLowerCase().trim();
    console.log('Voice command:', transcript);

    if (transcript.includes('start') && !cookingStarted) {
      startCooking();
    } else if (transcript.includes('next') || transcript.includes('continue')) {
      nextStep();
    } else if (transcript.includes('repeat') || transcript.includes('again')) {
      repeatCurrentStep();
    } else if (transcript.includes('previous') || transcript.includes('back')) {
      previousStep();
    } else if (transcript.includes('timer') && transcript.includes('minute')) {
      const minutes = extractMinutes(transcript);
      if (minutes) startTimer(minutes);
    } else if (transcript.includes('pause')) {
      pauseSpeech();
    } else if (transcript.includes('help')) {
      showHelp();
    } else if (transcript.includes('save') || transcript.includes('finish')) {
      finishCooking();
    } else if (transcript.includes('stop') || transcript.includes('exit')) {
      exitCookingMode();
    }
  };

  const extractMinutes = (text) => {
    const match = text.match(/(\d+)\s*minute/);
    return match ? parseInt(match[1]) : null;
  };

  const startCooking = () => {
    setCookingStarted(true);
    setCurrentStep(0);
    speak(`Great! Let's start cooking ${recipe.name}. Here are the ingredients you'll need: ${recipe.ingredients.join(', ')}. Say 'next' when you're ready for the first step.`);
  };

  const nextStep = () => {
    if (!cookingStarted) {
      speak("Please say 'start' first to begin cooking.");
      return;
    }

    if (currentStep < recipe.steps.length) {
      const stepIndex = currentStep;
      setCompletedSteps(prev => [...prev, stepIndex]);
      
      if (stepIndex < recipe.steps.length - 1) {
        setCurrentStep(stepIndex + 1);
        speakStep(stepIndex + 1);
      } else {
        speak("Congratulations! You've completed all the cooking steps. Say 'save' to save your cooking session or 'exit' to finish.");
      }
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      speakStep(currentStep - 1);
    } else {
      speak("You're already at the first step.");
    }
  };

  const repeatCurrentStep = () => {
    if (cookingStarted) {
      speakStep(currentStep);
    }
  };

  const speakStep = (stepIndex) => {
    if (recipe.steps[stepIndex]) {
      const stepText = `Step ${stepIndex + 1}: ${recipe.steps[stepIndex]}`;
      speak(stepText);

      const step = recipe.steps[stepIndex].toLowerCase();
      const timerMatch = step.match(/(\d+)\s*minute/);
      if (timerMatch) {
        const minutes = parseInt(timerMatch[1]);
        setTimeout(() => {
          speak(`I detected a ${minutes} minute timer in this step. Say 'timer ${minutes} minutes' to start it.`);
        }, 3000);
      }
    }
  };

  const startTimer = (minutes) => {
    const timerId = Date.now();
    const endTime = Date.now() + (minutes * 60 * 1000);
    
    setTimers(prev => ({
      ...prev,
      [timerId]: { minutes, endTime, active: true }
    }));

    speak(`Timer started for ${minutes} minutes.`);

    setTimeout(() => {
      setTimers(prev => ({
        ...prev,
        [timerId]: { ...prev[timerId], active: false }
      }));
      speak(`Timer finished! ${minutes} minutes are up.`);
      toast.success(`Timer finished! ${minutes} minutes are up.`);
    }, minutes * 60 * 1000);
  };

  const pauseSpeech = () => {
    if (synthRef.current) {
      synthRef.current.cancel();
      setIsSpeaking(false);
      speak("Speech paused.");
    }
  };

  const showHelp = () => {
    const helpText = `Available commands: Say 'start' to begin cooking, 'next' for next step, 'repeat' to repeat current step, 'previous' for previous step, 'timer X minutes' to start a timer, 'pause' to pause speech, 'save' to save your session, or 'exit' to stop cooking.`;
    speak(helpText);
  };

  const finishCooking = () => {
    setShowSaveModal(true);
    speak("Would you like to save your cooking session? You can add notes about how it went.");
  };

  const exitCookingMode = () => {
    speak("Exiting cooking mode. Happy cooking!");
    setTimeout(() => {
      onClose();
    }, 2000);
  };

  const saveCookingSession = () => {
    const session = {
      recipeId: recipe._id,
      recipeName: recipe.name,
      completedSteps: completedSteps.length,
      totalSteps: recipe.steps.length,
      notes: cookingNotes,
      date: new Date().toISOString(),
      userId: userData?.id
    };

    const savedSessions = JSON.parse(localStorage.getItem('cookingSessions') || '[]');
    savedSessions.push(session);
    localStorage.setItem('cookingSessions', JSON.stringify(savedSessions));

    toast.success('Cooking session saved successfully!');
    setShowSaveModal(false);
    speak("Cooking session saved! Great job cooking today.");
    setTimeout(() => onClose(), 2000);
  };

  const getActiveTimers = () => {
    return Object.entries(timers).filter(([_, timer]) => timer.active);
  };

  return (
    <div className="cooking-mode-overlay">
      <Card className="cooking-mode-card shadow-lg">
        <Card.Header className="bg-primary text-white">
          <div className="d-flex justify-content-between align-items-center">
            <h5 className="mb-0">
              <i className="fas fa-microphone me-2"></i>
              Voice Cooking Mode
            </h5>
            <Button variant="outline-light" size="sm" onClick={exitCookingMode}>
              <i className="fas fa-times"></i>
            </Button>
          </div>
        </Card.Header>
        
        <Card.Body>
          {/* Status Indicators */}
          <div className="mb-3">
            <Badge bg={isListening ? 'success' : 'secondary'} className="me-2">
              <i className="fas fa-microphone me-1"></i>
              {isListening ? 'Listening' : 'Not Listening'}
            </Badge>
            <Badge bg={isSpeaking ? 'warning' : 'secondary'} className="me-2">
              <i className="fas fa-volume-up me-1"></i>
              {isSpeaking ? 'Speaking' : 'Silent'}
            </Badge>
            {cookingStarted && (
              <Badge bg="info">
                Step {currentStep + 1} of {recipe.steps.length}
              </Badge>
            )}
          </div>

          {/* Active Timers */}
          {getActiveTimers().length > 0 && (
            <Alert variant="warning" className="mb-3">
              <i className="fas fa-clock me-2"></i>
              <strong>Active Timers:</strong>
              {getActiveTimers().map(([id, timer]) => (
                <Badge key={id} bg="warning" text="dark" className="ms-2">
                  {timer.minutes}m timer running
                </Badge>
              ))}
            </Alert>
          )}

          {/* Current Step Display */}
          {cookingStarted && recipe.steps[currentStep] && (
            <Card className="mb-3 current-step-card">
              <Card.Body>
                <h6>Current Step {currentStep + 1}:</h6>
                <p className="mb-0">{recipe.steps[currentStep]}</p>
              </Card.Body>
            </Card>
          )}

          {/* Voice Commands Help */}
          <Card className="bg-light">
            <Card.Body className="py-2">
              <small className="text-muted">
                <strong>Voice Commands:</strong> "start", "next", "repeat", "previous", "timer X minutes", "pause", "help", "save", "exit"
              </small>
            </Card.Body>
          </Card>
        </Card.Body>
      </Card>

      {/* Save Session Modal */}
      <Modal show={showSaveModal} onHide={() => setShowSaveModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Save Cooking Session</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>You completed {completedSteps.length} out of {recipe.steps.length} steps.</p>
          <div className="mb-3">
            <label className="form-label">Add notes about your cooking experience:</label>
            <textarea
              className="form-control"
              rows="3"
              value={cookingNotes}
              onChange={(e) => setCookingNotes(e.target.value)}
              placeholder="How did it go? Any modifications you made?"
            />
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowSaveModal(false)}>
            Skip
          </Button>
          <Button variant="primary" onClick={saveCookingSession}>
            Save Session
          </Button>
        </Modal.Footer>
      </Modal>

      <style jsx>{`
        .cooking-mode-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1050;
          padding: 20px;
        }
        
        .cooking-mode-card {
          max-width: 600px;
          width: 100%;
          max-height: 90vh;
          overflow-y: auto;
        }
        
        .current-step-card {
          border-left: 4px solid #0d6efd;
          background: linear-gradient(90deg, rgba(13,110,253,0.1) 0%, rgba(13,110,253,0.05) 100%);
        }
      `}</style>
    </div>
  );
};

export default CookingMode;

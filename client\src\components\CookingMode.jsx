import { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>, Badge, Alert, Modal } from 'react-bootstrap';
import { toast } from 'react-toastify';
import { getUserData } from '../utils/auth';

const CookingMode = ({ recipe, onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [cookingStarted, setCookingStarted] = useState(false);
  const [timers, setTimers] = useState({});
  const [completedSteps, setCompletedSteps] = useState([]);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [cookingNotes, setCookingNotes] = useState('');
  
  const recognitionRef = useRef(null);
  const synthRef = useRef(null);
  const userData = getUserData();

  useEffect(() => {
    initializeSpeechAPIs();
    startListening();
    
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (synthRef.current) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  const initializeSpeechAPIs = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onresult = handleVoiceCommand;
      recognitionRef.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        if (event.error !== 'no-speech') {
          toast.error('Voice recognition error. Please try again.');
        }
      };
    }

    synthRef.current = window.speechSynthesis;
  };

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      setIsListening(true);
      recognitionRef.current.start();
      speak("Welcome to voice cooking! Say 'start' to begin your cooking journey.");
    }
  };

  const speak = (text, callback = null) => {
    if (synthRef.current) {
      setIsSpeaking(true);
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = 'en-US'; // Set to English
      utterance.rate = 0.9;
      utterance.pitch = 1;
      utterance.volume = 0.8;

      utterance.onend = () => {
        setIsSpeaking(false);
        if (callback) callback();
      };

      utterance.onerror = () => {
        setIsSpeaking(false);
        console.error('Speech synthesis error');
      };

      synthRef.current.speak(utterance);
    }
  };

  const handleVoiceCommand = (event) => {
    const transcript = event.results[event.results.length - 1][0].transcript.toLowerCase().trim();
    console.log('Voice command:', transcript);

    if (transcript.includes('start') && !cookingStarted) {
      startCooking();
    } else if (transcript.includes('next') || transcript.includes('continue')) {
      nextStep();
    } else if (transcript.includes('repeat') || transcript.includes('again')) {
      repeatCurrentStep();
    } else if (transcript.includes('previous') || transcript.includes('back')) {
      previousStep();
    } else if (transcript.includes('timer') && transcript.includes('minute')) {
      const minutes = extractMinutes(transcript);
      if (minutes) startTimer(minutes);
    } else if (transcript.includes('pause')) {
      pauseSpeech();
    } else if (transcript.includes('help')) {
      showHelp();
    } else if (transcript.includes('save') || transcript.includes('finish')) {
      finishCooking();
    } else if (transcript.includes('stop') || transcript.includes('exit')) {
      exitCookingMode();
    }
  };

  const extractMinutes = (text) => {
    const match = text.match(/(\d+)\s*minute/);
    return match ? parseInt(match[1]) : null;
  };

  const startCooking = () => {
    setCookingStarted(true);
    setCurrentStep(0);
    const ingredientsList = recipe.ingredients?.length > 0
      ? recipe.ingredients.slice(0, 3).join(', ') + (recipe.ingredients.length > 3 ? ' and more' : '')
      : 'the ingredients shown on screen';
    speak(`Perfect! Let's cook ${recipe.name}. You'll need ${ingredientsList}. Say 'next' when you're ready for step one.`);
  };

  const nextStep = () => {
    if (!cookingStarted) {
      speak("Please say 'start' first to begin cooking.");
      return;
    }

    if (currentStep < recipe.steps.length) {
      const stepIndex = currentStep;
      setCompletedSteps(prev => [...prev, stepIndex]);
      
      if (stepIndex < recipe.steps.length - 1) {
        setCurrentStep(stepIndex + 1);
        speakStep(stepIndex + 1);
      } else {
        speak("Congratulations! You've completed all the cooking steps. Say 'save' to save your cooking session or 'exit' to finish.");
      }
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      speakStep(currentStep - 1);
    } else {
      speak("You're already at the first step.");
    }
  };

  const repeatCurrentStep = () => {
    if (cookingStarted) {
      speakStep(currentStep);
    }
  };

  const speakStep = (stepIndex) => {
    if (recipe.steps[stepIndex]) {
      const stepText = `Step ${stepIndex + 1}: ${recipe.steps[stepIndex]}`;
      speak(stepText);

      const step = recipe.steps[stepIndex].toLowerCase();
      const timerMatch = step.match(/(\d+)\s*minute/);
      if (timerMatch) {
        const minutes = parseInt(timerMatch[1]);
        setTimeout(() => {
          speak(`I detected a ${minutes} minute timer in this step. Say 'timer ${minutes} minutes' to start it.`);
        }, 3000);
      }
    }
  };

  const startTimer = (minutes) => {
    const timerId = Date.now();
    const endTime = Date.now() + (minutes * 60 * 1000);
    
    setTimers(prev => ({
      ...prev,
      [timerId]: { minutes, endTime, active: true }
    }));

    speak(`Timer started for ${minutes} minutes.`);

    setTimeout(() => {
      setTimers(prev => ({
        ...prev,
        [timerId]: { ...prev[timerId], active: false }
      }));
      speak(`Timer finished! ${minutes} minutes are up.`);
      toast.success(`Timer finished! ${minutes} minutes are up.`);
    }, minutes * 60 * 1000);
  };

  const pauseSpeech = () => {
    if (synthRef.current) {
      synthRef.current.cancel();
      setIsSpeaking(false);
      speak("Speech paused.");
    }
  };

  const showHelp = () => {
    const helpText = `Available commands: Say 'start' to begin cooking, 'next' for next step, 'repeat' to repeat current step, 'previous' for previous step, 'timer X minutes' to start a timer, 'pause' to pause speech, 'save' to save your session, or 'exit' to stop cooking.`;
    speak(helpText);
  };

  const finishCooking = () => {
    setShowSaveModal(true);
    speak("Would you like to save your cooking session? You can add notes about how it went.");
  };

  const exitCookingMode = () => {
    speak("Exiting cooking mode. Happy cooking!");
    setTimeout(() => {
      onClose();
    }, 2000);
  };

  const saveCookingSession = () => {
    const session = {
      recipeId: recipe._id,
      recipeName: recipe.name,
      completedSteps: completedSteps.length,
      totalSteps: recipe.steps.length,
      notes: cookingNotes,
      date: new Date().toISOString(),
      userId: userData?.id
    };

    const savedSessions = JSON.parse(localStorage.getItem('cookingSessions') || '[]');
    savedSessions.push(session);
    localStorage.setItem('cookingSessions', JSON.stringify(savedSessions));

    toast.success('Cooking session saved successfully!');
    setShowSaveModal(false);
    speak("Cooking session saved! Great job cooking today.");
    setTimeout(() => onClose(), 2000);
  };

  const getActiveTimers = () => {
    return Object.entries(timers).filter(([_, timer]) => timer.active);
  };

  return (
    <div className="cooking-mode-overlay">
      <Card className="cooking-mode-card shadow-lg">
        <Card.Header className="bg-gradient-primary text-white">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h5 className="mb-0">
                <i className="fas fa-chef-hat me-2"></i>
                {recipe.name}
              </h5>
              <small className="opacity-75">Voice-Guided Cooking</small>
            </div>
            <Button variant="outline-light" size="sm" onClick={exitCookingMode}>
              <i className="fas fa-times"></i>
            </Button>
          </div>
        </Card.Header>
        
        <Card.Body>
          {/* Status Indicators */}
          {!cookingStarted ? (
            <div className="text-center py-4">
              <i className="fas fa-play-circle fa-4x text-primary mb-3"></i>
              <h4>Ready to Cook?</h4>
              <p className="text-muted mb-4">Say "start" to begin your voice-guided cooking experience</p>
              <div className="d-flex justify-content-center mb-3">
                <Badge bg={isListening ? 'success' : 'secondary'} className="px-3 py-2">
                  <i className="fas fa-microphone me-2"></i>
                  {isListening ? 'Listening for "start"...' : 'Voice inactive'}
                </Badge>
              </div>
            </div>
          ) : (
            <>
              <div className="d-flex justify-content-between align-items-center mb-3">
                <Badge bg="primary" className="px-3 py-2">
                  <i className="fas fa-list-ol me-2"></i>
                  Step {currentStep + 1} of {recipe.steps?.length || 0}
                </Badge>
                <div className="d-flex gap-2">
                  <Badge bg={isListening ? 'success' : 'secondary'}>
                    <i className="fas fa-microphone me-1"></i>
                    {isListening ? 'Listening' : 'Paused'}
                  </Badge>
                  {isSpeaking && (
                    <Badge bg="warning">
                      <i className="fas fa-volume-up me-1"></i>
                      Speaking
                    </Badge>
                  )}
                </div>
              </div>


              {getActiveTimers().length > 0 && (
                <Alert variant="success" className="mb-3">
                  <div className="d-flex align-items-center">
                    <i className="fas fa-stopwatch fa-2x me-3 text-success"></i>
                    <div>
                      <strong>Active Timers</strong>
                      <div>
                        {getActiveTimers().map(([id, timer]) => (
                          <Badge key={id} bg="success" className="ms-2">
                            {timer.minutes}m running
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </Alert>
              )}

              {recipe.steps && recipe.steps[currentStep] && (
                <Card className="current-step-card mb-3">
                  <Card.Body>
                    <div className="d-flex align-items-start">
                      <div className="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                           style={{ width: '40px', height: '40px', minWidth: '40px' }}>
                        <strong>{currentStep + 1}</strong>
                      </div>
                      <div className="flex-grow-1">
                        <h6 className="mb-2">Current Step</h6>
                        <p className="mb-0 lead">{recipe.steps[currentStep]}</p>
                      </div>
                    </div>
                  </Card.Body>
                </Card>
              )}

              <div className="text-center">
                <small className="text-muted">
                  <i className="fas fa-lightbulb me-1"></i>
                  Say: <strong>"next"</strong>, <strong>"repeat"</strong>, <strong>"timer 5 minutes"</strong>, or <strong>"save"</strong>
                </small>
              </div>
            </>
          )}
        </Card.Body>
      </Card>


      <Modal show={showSaveModal} onHide={() => setShowSaveModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Save Cooking Session</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>You completed {completedSteps.length} out of {recipe.steps.length} steps.</p>
          <div className="mb-3">
            <label className="form-label">Add notes about your cooking experience:</label>
            <textarea
              className="form-control"
              rows="3"
              value={cookingNotes}
              onChange={(e) => setCookingNotes(e.target.value)}
              placeholder="How did it go? Any modifications you made?"
            />
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowSaveModal(false)}>
            Skip
          </Button>
          <Button variant="primary" onClick={saveCookingSession}>
            Save Session
          </Button>
        </Modal.Footer>
      </Modal>

      <style jsx>{`
        .cooking-mode-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1050;
          padding: 20px;
        }
        
        .cooking-mode-card {
          max-width: 600px;
          width: 100%;
          max-height: 90vh;
          overflow-y: auto;
        }
        
        .current-step-card {
          border-left: 4px solid #0d6efd;
          background: linear-gradient(90deg, rgba(13,110,253,0.1) 0%, rgba(13,110,253,0.05) 100%);
        }
      `}</style>
    </div>
  );
};

export default CookingMode;

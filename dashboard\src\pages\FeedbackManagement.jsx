import { useState, useEffect } from 'react';
import { Row, Col, Card, Table, Badge, Form, InputGroup } from 'react-bootstrap';
import { toast } from 'react-toastify';
import axiosInstance from '../utils/axiosConfig';
import { FEEDBACK_ROUTES } from '../utils/apiRoutes';

const FeedbackManagement = () => {
  const [feedback, setFeedback] = useState([]);
  const [filteredFeedback, setFilteredFeedback] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRating, setFilterRating] = useState('');
  const [stats, setStats] = useState({
    total: 0,
    averageRating: 0,
    ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
  });

  useEffect(() => {
    fetchFeedback();
  }, []);

  useEffect(() => {
    filterFeedback();
  }, [feedback, searchTerm, filterRating]);

  const fetchFeedback = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(FEEDBACK_ROUTES.GET_ALL);
      if (response.data.success) {
        const feedbacks = response.data.feedbacks || [];
        setFeedback(feedbacks);
        calculateStats(feedbacks);
      }
    } catch (error) {
      console.error('Error fetching feedback:', error);
      toast.error('Failed to fetch feedback');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (feedbacks) => {
    const total = feedbacks.length;
    const averageRating = total > 0 
      ? (feedbacks.reduce((sum, fb) => sum + fb.rating, 0) / total).toFixed(1)
      : 0;
    
    const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    feedbacks.forEach(fb => {
      ratingDistribution[fb.rating]++;
    });

    setStats({ total, averageRating, ratingDistribution });
  };

  const filterFeedback = () => {
    let filtered = feedback.filter(fb => {
      const matchesSearch = 
        (fb.recipe?.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (fb.user?.username || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        fb.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesRating = filterRating === '' || fb.rating.toString() === filterRating;
      
      return matchesSearch && matchesRating;
    });

    setFilteredFeedback(filtered);
  };

  const renderStars = (rating) => {
    return (
      <div className="d-flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <i
            key={star}
            className={`fas fa-star ${star <= rating ? 'text-warning' : 'text-muted'}`}
            style={{ fontSize: '0.875rem' }}
          />
        ))}
      </div>
    );
  };

  return (
    <>
      {/* Page Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex align-items-center justify-content-between">
            <div>
              <h1 className="h3 mb-1">Feedback Management</h1>
              <p className="text-muted mb-0">
                Monitor and analyze user feedback and ratings
              </p>
            </div>
          </div>
        </Col>
      </Row>

      {/* Stats Cards */}
      <Row className="mb-4">
        <Col lg={3} md={6}>
          <Card className="border-0 shadow-sm h-100 stats-card">
            <Card.Body className="text-center">
              <div className="text-primary mb-2">
                <i className="fas fa-comments fa-2x"></i>
              </div>
              <h3 className="h4 mb-1">{stats.total}</h3>
              <p className="text-muted mb-0">Total Reviews</p>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={3} md={6}>
          <Card className="border-0 shadow-sm h-100 stats-card">
            <Card.Body className="text-center">
              <div className="text-warning mb-2">
                <i className="fas fa-star fa-2x"></i>
              </div>
              <h3 className="h4 mb-1">{stats.averageRating}</h3>
              <p className="text-muted mb-0">Average Rating</p>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={6}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Body>
              <h6 className="mb-3">Rating Distribution</h6>
              {[5, 4, 3, 2, 1].map(rating => (
                <div key={rating} className="d-flex align-items-center mb-2">
                  <span className="me-2">{rating}</span>
                  <i className="fas fa-star text-warning me-2"></i>
                  <div className="progress flex-grow-1 me-2" style={{ height: '8px' }}>
                    <div
                      className="progress-bar bg-warning"
                      style={{
                        width: `${stats.total > 0 ? (stats.ratingDistribution[rating] / stats.total) * 100 : 0}%`
                      }}
                    ></div>
                  </div>
                  <small className="text-muted">{stats.ratingDistribution[rating]}</small>
                </div>
              ))}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Row className="mb-4">
        <Col md={8}>
          <InputGroup size="lg">
            <InputGroup.Text>
              <i className="fas fa-search"></i>
            </InputGroup.Text>
            <Form.Control
              type="text"
              placeholder="Search by recipe name, user, or feedback content..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
        </Col>
        <Col md={4}>
          <Form.Select
            size="lg"
            value={filterRating}
            onChange={(e) => setFilterRating(e.target.value)}
          >
            <option value="">All Ratings</option>
            <option value="5">5 Stars</option>
            <option value="4">4 Stars</option>
            <option value="3">3 Stars</option>
            <option value="2">2 Stars</option>
            <option value="1">1 Star</option>
          </Form.Select>
        </Col>
      </Row>

      {/* Feedback Table */}
      <Card className="border-0 shadow-sm">
        <Card.Header className="bg-white border-bottom">
          <h5 className="mb-0">
            <i className="fas fa-comments me-2"></i>
            All Feedback ({filteredFeedback.length})
          </h5>
        </Card.Header>
        <Card.Body>
          {loading ? (
            <div className="text-center py-5">
              <div className="spinner-border text-primary mb-3" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="text-muted">Loading feedback...</p>
            </div>
          ) : filteredFeedback.length > 0 ? (
            <Table responsive hover className="mb-0">
              <thead>
                <tr>
                  <th>Recipe</th>
                  <th>User</th>
                  <th>Rating</th>
                  <th>Feedback</th>
                  <th>Date</th>
                </tr>
              </thead>
              <tbody>
                {filteredFeedback.map((fb) => (
                  <tr key={fb._id}>
                    <td>
                      <div className="d-flex align-items-center">
                        {fb.recipe?.image && (
                          <img
                            src={fb.recipe.image}
                            alt={fb.recipe.name}
                            className="rounded me-2"
                            style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                          />
                        )}
                        <div>
                          <div className="fw-semibold">{fb.recipe?.name || 'Unknown Recipe'}</div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <Badge bg="secondary">
                        {fb.user?.username || 'Anonymous'}
                      </Badge>
                    </td>
                    <td>
                      <div className="d-flex align-items-center">
                        {renderStars(fb.rating)}
                        <span className="ms-2 small">{fb.rating}/5</span>
                      </div>
                    </td>
                    <td>
                      <div style={{ maxWidth: '300px' }}>
                        {fb.description.length > 100
                          ? `${fb.description.substring(0, 100)}...`
                          : fb.description}
                      </div>
                    </td>
                    <td>
                      <small className="text-muted">
                        {new Date(fb.createdAt).toLocaleDateString()}
                      </small>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          ) : (
            <div className="text-center py-5">
              <div className="text-muted">
                {searchTerm || filterRating ? (
                  <>
                    <i className="fas fa-search fa-3x mb-3"></i>
                    <h4>No feedback found</h4>
                    <p>Try adjusting your search criteria.</p>
                  </>
                ) : (
                  <>
                    <i className="fas fa-comments fa-3x mb-3"></i>
                    <h4>No feedback yet</h4>
                    <p>Feedback will appear here once users start reviewing recipes.</p>
                  </>
                )}
              </div>
            </div>
          )}
        </Card.Body>
      </Card>
    </>
  );
};

export default FeedbackManagement;

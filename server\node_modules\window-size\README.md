# window-size [![NPM version](https://badge.fury.io/js/window-size.svg)](http://badge.fury.io/js/window-size)  [![Build Status](https://travis-ci.org/jonschlinkert/window-size.svg)](https://travis-ci.org/jonschlinkert/window-size)

> Reliable way to to get the height and width of the terminal/console in a node.js environment.

## Install

Install with [npm](https://www.npmjs.com/)

```sh
$ npm i window-size --save
```

## Usage

```js
var size = require('window-size');
size.height; // "25" (rows)
size.width; // "80" (columns)
```

## Other projects

* [base-cli](https://www.npmjs.com/package/base-cli): Plugin for base-methods that maps built-in methods to CLI args (also supports methods from a… [more](https://www.npmjs.com/package/base-cli) | [homepage](https://github.com/jonschlinkert/base-cli)
* [lint-deps](https://www.npmjs.com/package/lint-deps): CLI tool that tells you when dependencies are missing from package.json and offers you a… [more](https://www.npmjs.com/package/lint-deps) | [homepage](https://github.com/jonschlinkert/lint-deps)
* [yargs](https://www.npmjs.com/package/yargs): Light-weight option parsing with an argv hash. No optstrings attached. | [homepage](https://github.com/bcoe/yargs#readme)

## Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/window-size/issues/new).

## Author

**Jon Schlinkert**

+ [github/jonschlinkert](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

## License

Copyright © 2014-2015 [Jon Schlinkert](https://github.com/jonschlinkert)
Released under the MIT license.

***

_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on November 15, 2015._
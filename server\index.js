const express = require("express");
const app = express();
const path = require("path");

require("dotenv").config();
const cookieParser = require("cookie-parser");
const cors = require("cors");
const fileUpload = require("express-fileupload");

const dbconnect = require("./config/database");
dbconnect();

const { cloudinaryConnect } = require("./config/cloudinary");
cloudinaryConnect();
app.use(express.json({ limit: '10mb' }));
app.use(cookieParser());
app.use(cors({
  origin: ["http://localhost:5173", "http://localhost:5174"],
  credentials: true,
}));
app.use(fileUpload({
  useTempFiles: true,
  tempFileDir: "/tmp/",
}));

// Serve static audio files
app.use('/audio', express.static(path.join(__dirname, 'public/audio')));

const assistantRoutes = require("./routes/assistantRoute");
const recipeRoutes = require("./routes/recipeRoutes");
const authRoutes = require("./routes/authRoutes");
const feedbackRoutes = require("./routes/feedbackRoute");

app.use("/assistant",assistantRoutes);
app.use("/recipe",recipeRoutes);
app.use("/",authRoutes);
app.use("/feedback",feedbackRoutes);

const PORT = process.env.PORT || 4000;
app.listen(PORT,()=>{
  console.log(`Server is listening on PORT ${PORT}`);
})
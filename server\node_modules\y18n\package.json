{"name": "y18n", "version": "3.2.2", "description": "the bare-bones internationalization library used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "**************:yargs/y18n.git"}, "files": ["index.js"], "keywords": ["i18n", "internationalization", "yargs"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "homepage": "https://github.com/yargs/y18n", "devDependencies": {"chai": "^3.4.1", "coveralls": "^2.11.6", "mocha": "^3.0.0", "nyc": "^10.0.0", "rimraf": "^2.5.0", "standard": "^10.0.0-beta.0"}}
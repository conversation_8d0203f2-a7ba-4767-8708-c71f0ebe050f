// Voice Assistant utility functions using Web Speech API

class VoiceAssistant {
  constructor() {
    this.synthesis = window.speechSynthesis;
    this.recognition = null;
    this.isListening = false;
    
    // Initialize speech recognition if available
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      this.recognition = new SpeechRecognition();
      this.recognition.continuous = false;
      this.recognition.interimResults = false;
      this.recognition.lang = 'hi-IN'; // Hindi language
    }
  }

  // Text-to-Speech function
  speak(text, lang = 'hi-IN') {
    return new Promise((resolve, reject) => {
      if (!this.synthesis) {
        reject(new Error('Speech synthesis not supported'));
        return;
      }

      // Cancel any ongoing speech
      this.synthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = lang;
      utterance.rate = 0.8; // Slightly slower for cooking instructions
      utterance.pitch = 1;
      utterance.volume = 1;

      utterance.onend = () => resolve();
      utterance.onerror = (error) => reject(error);

      this.synthesis.speak(utterance);
    });
  }

  // Stop current speech
  stopSpeaking() {
    if (this.synthesis) {
      this.synthesis.cancel();
    }
  }

  // Speech-to-Text function
  listen() {
    return new Promise((resolve, reject) => {
      if (!this.recognition) {
        reject(new Error('Speech recognition not supported'));
        return;
      }

      if (this.isListening) {
        reject(new Error('Already listening'));
        return;
      }

      this.isListening = true;

      this.recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript.toLowerCase();
        this.isListening = false;
        resolve(transcript);
      };

      this.recognition.onerror = (error) => {
        this.isListening = false;
        reject(error);
      };

      this.recognition.onend = () => {
        this.isListening = false;
      };

      this.recognition.start();
    });
  }

  // Stop listening
  stopListening() {
    if (this.recognition && this.isListening) {
      this.recognition.stop();
      this.isListening = false;
    }
  }

  // Check if voice commands match common cooking commands
  parseVoiceCommand(transcript) {
    const command = transcript.toLowerCase();
    
    if (command.includes('next') || command.includes('अगला')) {
      return 'next';
    } else if (command.includes('repeat') || command.includes('दोहराएं')) {
      return 'repeat';
    } else if (command.includes('pause') || command.includes('रोकें')) {
      return 'pause';
    } else if (command.includes('start') || command.includes('शुरू')) {
      return 'start';
    }
    
    return null;
  }
}

export default VoiceAssistant;

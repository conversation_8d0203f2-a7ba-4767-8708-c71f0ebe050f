import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, Button, Navbar, Nav } from 'react-bootstrap';
import { isAuthenticated, getUserData, clearUserData } from '../utils/auth';
import axiosInstance from '../utils/axiosConfig';
import { RECIPE_ROUTES } from '../utils/apiRoutes';
import { toast } from 'react-toastify';

const Home = () => {
  const [recipes, setRecipes] = useState([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const isLoggedIn = isAuthenticated();
  const userData = getUserData();

  useEffect(() => {
    if (isLoggedIn) {
      fetchRecipes();
    }
  }, [isLoggedIn]);

  const fetchRecipes = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(RECIPE_ROUTES.GET_ALL);
      if (response.data.success) {
        setRecipes(response.data.recipes);
      }
    } catch (error) {
      console.error('Error fetching recipes:', error);
      toast.error('Failed to fetch recipes');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    clearUserData();
    toast.success('Logged out successfully');
    navigate('/');
  };

  return (
    <>
      {/* Navigation Bar */}
      <Navbar bg="primary" variant="dark" expand="lg" className="mb-4">
        <Container>
          <Navbar.Brand href="/">
            <i className="fas fa-utensils me-2"></i>
            Cookify
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="ms-auto">
              {isLoggedIn ? (
                <>
                  <Nav.Link className="text-white">
                    Welcome, {userData?.username}!
                  </Nav.Link>
                  <Button variant="outline-light" onClick={handleLogout}>
                    Logout
                  </Button>
                </>
              ) : (
                <>
                  <Nav.Link as={Link} to="/login" className="text-white">
                    Login
                  </Nav.Link>
                  <Nav.Link as={Link} to="/signup" className="text-white">
                    Sign Up
                  </Nav.Link>
                </>
              )}
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>

      <Container>
        {!isLoggedIn ? (
          // Welcome Section for Non-Authenticated Users
          <Row className="justify-content-center text-center">
            <Col md={8}>
              <div className="hero-section py-5">
                <h1 className="display-4 mb-4">
                  Welcome to <span className="text-primary">Cookify</span>
                </h1>
                <p className="lead mb-4">
                  Your voice-enabled cooking assistant with step-by-step guidance in Hindi
                </p>
                <div className="d-flex justify-content-center gap-3">
                  <Button as={Link} to="/login" variant="primary" size="lg">
                    Login
                  </Button>
                  <Button as={Link} to="/signup" variant="outline-primary" size="lg">
                    Sign Up
                  </Button>
                </div>
              </div>
            </Col>
          </Row>
        ) : (
          // Recipe Browsing Section for Authenticated Users
          <>
            <Row className="mb-4">
              <Col>
                <h2>Discover Delicious Recipes</h2>
                <p className="text-muted">
                  Browse our collection of recipes with voice-guided cooking instructions
                </p>
              </Col>
            </Row>

            {loading ? (
              <Row className="justify-content-center">
                <Col md={6} className="text-center">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  <p className="mt-2">Loading recipes...</p>
                </Col>
              </Row>
            ) : (
              <Row>
                {recipes.length > 0 ? (
                  recipes.map((recipe) => (
                    <Col md={4} className="mb-4" key={recipe._id}>
                      <Card className="h-100 shadow-sm">
                        <Card.Img
                          variant="top"
                          src={recipe.image}
                          alt={recipe.name}
                          style={{ height: '200px', objectFit: 'cover' }}
                        />
                        <Card.Body className="d-flex flex-column">
                          <Card.Title>{recipe.name}</Card.Title>
                          <Card.Text className="flex-grow-1">
                            {recipe.description.length > 100
                              ? `${recipe.description.substring(0, 100)}...`
                              : recipe.description}
                          </Card.Text>
                          <Button
                            as={Link}
                            to={`/recipe/${recipe._id}`}
                            variant="primary"
                            className="mt-auto"
                          >
                            View Recipe
                          </Button>
                        </Card.Body>
                      </Card>
                    </Col>
                  ))
                ) : (
                  <Col md={12} className="text-center">
                    <p className="text-muted">No recipes available at the moment.</p>
                  </Col>
                )}
              </Row>
            )}
          </>
        )}
      </Container>
    </>
  );
};

export default Home;

import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Row, Col, Card, Button, Badge, Spinner } from 'react-bootstrap';
import { getUserData } from '../utils/auth';
import axiosInstance from '../utils/axiosConfig';
import { RECIPE_ROUTES } from '../utils/apiRoutes';
import { toast } from 'react-toastify';

const Home = () => {
  const [recipes, setRecipes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalRecipes: 0,
    recentRecipes: 0,
    favoriteCount: 0
  });
  const userData = getUserData();

  useEffect(() => {
    fetchRecipes();
  }, []);

  const fetchRecipes = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(RECIPE_ROUTES.GET_ALL);
      if (response.data.success) {
        const allRecipes = response.data.recipes;
        setRecipes(allRecipes.slice(0, 6)); // Show only 6 recent recipes on dashboard

        // Calculate stats
        const now = new Date();
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const recentCount = allRecipes.filter(recipe =>
          new Date(recipe.createdAt) > weekAgo
        ).length;

        setStats({
          totalRecipes: allRecipes.length,
          recentRecipes: recentCount,
          favoriteCount: Math.floor(allRecipes.length * 0.3) // Mock favorite count
        });
      }
    } catch (error) {
      console.error('Error fetching recipes:', error);
      toast.error('Failed to fetch recipes');
    } finally {
      setLoading(false);
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  return (
    <>
      {/* Welcome Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex align-items-center justify-content-between">
            <div>
              <h1 className="h3 mb-1">
                {getGreeting()}, {userData?.username}! 👋
              </h1>
              <p className="text-muted mb-0">
                Ready to cook something delicious today?
              </p>
            </div>
            <div className="d-none d-md-block">
              <Button as={Link} to="/recipes" variant="primary" size="lg">
                <i className="fas fa-book me-2"></i>
                Browse All Recipes
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {/* Stats Cards */}
      <Row className="mb-4">
        <Col md={4}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <div className="text-primary mb-2">
                <i className="fas fa-book fa-2x"></i>
              </div>
              <h3 className="h4 mb-1">{stats.totalRecipes}</h3>
              <p className="text-muted mb-0">Total Recipes</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={4}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <div className="text-success mb-2">
                <i className="fas fa-clock fa-2x"></i>
              </div>
              <h3 className="h4 mb-1">{stats.recentRecipes}</h3>
              <p className="text-muted mb-0">New This Week</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={4}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <div className="text-warning mb-2">
                <i className="fas fa-heart fa-2x"></i>
              </div>
              <h3 className="h4 mb-1">{stats.favoriteCount}</h3>
              <p className="text-muted mb-0">Popular Recipes</p>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Recent Recipes Section */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex align-items-center justify-content-between mb-3">
            <h2 className="h4 mb-0">Recent Recipes</h2>
            <Button as={Link} to="/recipes" variant="outline-primary">
              View All <i className="fas fa-arrow-right ms-1"></i>
            </Button>
          </div>
        </Col>
      </Row>

      {loading ? (
        <Row className="justify-content-center">
          <Col md={6} className="text-center py-5">
            <Spinner animation="border" variant="primary" className="mb-3" />
            <p className="text-muted">Loading your recipes...</p>
          </Col>
        </Row>
      ) : (
        <Row>
          {recipes.length > 0 ? (
            recipes.map((recipe) => (
              <Col lg={4} md={6} className="mb-4" key={recipe._id}>
                <Card className="border-0 shadow-sm h-100 recipe-card">
                  <div className="position-relative">
                    <Card.Img
                      variant="top"
                      src={recipe.image}
                      alt={recipe.name}
                      style={{ height: '220px', objectFit: 'cover' }}
                    />
                    <Badge
                      bg="primary"
                      className="position-absolute top-0 end-0 m-2"
                    >
                      {recipe.steps?.length || 0} Steps
                    </Badge>
                  </div>
                  <Card.Body className="d-flex flex-column">
                    <Card.Title className="h5 mb-2">{recipe.name}</Card.Title>
                    <Card.Text className="text-muted flex-grow-1 small">
                      {recipe.description.length > 120
                        ? `${recipe.description.substring(0, 120)}...`
                        : recipe.description}
                    </Card.Text>
                    <div className="d-flex align-items-center justify-content-between mt-3">
                      <div className="d-flex align-items-center text-muted small">
                        <img
                          src={recipe.createdBy?.image || '/default-avatar.png'}
                          alt="Chef"
                          className="rounded-circle me-2"
                          style={{ width: '24px', height: '24px', objectFit: 'cover' }}
                        />
                        {recipe.createdBy?.username || 'Unknown Chef'}
                      </div>
                      <Button
                        as={Link}
                        to={`/recipe/${recipe._id}`}
                        variant="primary"
                        size="sm"
                      >
                        <i className="fas fa-play me-1"></i>
                        Cook Now
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            ))
          ) : (
            <Col md={12} className="text-center py-5">
              <div className="text-muted">
                <i className="fas fa-utensils fa-3x mb-3"></i>
                <h4>No recipes available</h4>
                <p>Check back later for new recipes!</p>
              </div>
            </Col>
          )}
        </Row>
      )}

      {/* Mobile Browse Button */}
      <Row className="d-md-none">
        <Col>
          <Button as={Link} to="/recipes" variant="primary" size="lg" className="w-100">
            <i className="fas fa-book me-2"></i>
            Browse All Recipes
          </Button>
        </Col>
      </Row>
    </>
  );
};

export default Home;

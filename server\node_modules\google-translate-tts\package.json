{"name": "google-translate-tts", "version": "0.4.0-dev", "description": "Text-to-speech using Google Translate", "main": "src/index.js", "repository": "https://github.com/ncpierson/google-translate-tts", "author": "<PERSON>", "license": "MIT", "private": false, "devDependencies": {"jest": "^26.6.3", "prettier": "^2.2.1"}, "scripts": {"tts": "scripts/tts.sh", "test": "jest"}, "node-red": {"nodes": {"text-to-speech": "src/index.nodered.js"}}, "keywords": ["tts", "text-to-speech", "google", "translate", "node-red"]}
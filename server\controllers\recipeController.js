const Recipe = require("../models/Recipe");
const User = require("../models/User");
const { uploadImagetoCloudinary } = require("../utils/imageUploader");
require("dotenv").config();

exports.createRecipe = async (req, res) => {
  try {
    const { name, description, ingredients, image, steps } = req.body;
    const userId = req.user.id;

    if (!name || !description || !ingredients || !image || !steps) {
      return res.status(400).json({ 
        success: false, 
        message: "All fields are required" 
      });
    }

    const user = await User.findById(userId);
    if (!user || user.role !== "Chef") {
      return res.status(403).json({ 
        success: false, 
        message: "Only chefs can create recipes" 
      });
    }

    const uploadedImage = await uploadImagetoCloudinary(image, "recipe-image");

    const newRecipe = await Recipe.create({
      name,
      description,
      ingredients,
      steps,
      image: uploadedImage.secure_url,
      createdBy: userId,
    });

    return res.status(201).json({
      success: true,
      message: "Recipe created successfully",
      recipe: newRecipe,
    });
  } catch (error) {
    console.error("Create Recipe Error:", error);
    return res.status(500).json({ 
      success: false, 
      message: "Server error" 
    });
  }
};


exports.getAllRecipe = async (req, res) => {
  try {
    const allRecipes = await Recipe.find({}).populate("createdBy", "username email image");
    return res.status(200).json({
      success: true,
      recipes: allRecipes,
    });
  } catch (error) {
    console.error("Get All Recipes Error:", error);
    return res.status(500).json({ 
      success: false, 
      message: "Server error" 
    });
  }
};

exports.getRecipe = async (req, res) => {
  try {
    const recipeId = req.params.id;
    const recipe = await Recipe.findById(recipeId).populate("createdBy", "username email image");
    if (!recipe) {
      return res.status(404).json({ 
        success: false, 
        message: "Recipe not found" 
      });
    }
    return res.status(200).json({ 
      success: true, 
      recipe 
    });
  } catch (error) {
    console.error("Get Recipe Error:", error);
    return res.status(500).json({ 
      success: false, 
      message: "Server error" 
    });
  }
};


exports.updateRecipe = async (req, res) => {
  try {
    const recipeId = req.params.id;
    const userId = req.user.id;
    const { name, description, ingredients, image, steps } = req.body;

    const user = await User.findById(userId);
    if (user.role === "User") {
      return res.status(403).json({ 
        success: false, 
        message: "Only chefs can update recipes" 
      });
    }

    let updatedFields = { name, description, ingredients, steps };

    if (image) {
      const uploadedImage = await uploadImagetoCloudinary(image, "recipe-image");
      updatedFields.image = uploadedImage.secure_url;
    }

    const updatedRecipe = await Recipe.findByIdAndUpdate(recipeId, updatedFields, { new: true });
    return res.status(200).json({
      success: true,
      message: "Recipe updated successfully",
      recipe: updatedRecipe,
    });
  } catch (error) {
    console.error("Update Recipe Error:", error);
    return res.status(500).json({ 
      success: false, 
      message: "Server error" 
    });
  }
};


exports.deleteRecipe = async (req, res) => {
  try {
    const recipeId = req.params.id;
    const userId = req.user.id;

    const user = await User.findById(userId);
    if (user.role === "User") {
      return res.status(403).json({ 
        success: false, 
        message: "Only chefs can delete recipes" 
      });
    }

    const recipe = await Recipe.findById(recipeId);
    if (!recipe) {
      return res.status(404).json({ 
        success: false, 
        message: "Recipe not found" 
      });
    }

    await Recipe.findByIdAndDelete(recipeId);
    return res.status(200).json({
      success: true,
      message: "Recipe deleted successfully",
    });
  } catch (error) {
    console.error("Delete Recipe Error:", error);
    return res.status(500).json({ 
      success: false, 
      message: "Server error" 
    });
  }
};

// Authentication utility functions

export const isAuthenticated = () => {
  // Check if user data exists in localStorage
  const userData = localStorage.getItem('cookify_dashboard_user');
  return userData !== null;
};

export const getUserData = () => {
  const userData = localStorage.getItem('cookify_dashboard_user');
  return userData ? JSON.parse(userData) : null;
};

export const setUserData = (user) => {
  localStorage.setItem('cookify_dashboard_user', JSON.stringify(user));
};

export const clearUserData = () => {
  localStorage.removeItem('cookify_dashboard_user');
};

export const getUserRole = () => {
  const userData = getUserData();
  return userData ? userData.role : null;
};

export const isUser = () => {
  return getUserRole() === 'User';
};

export const isChef = () => {
  return getUserRole() === 'Chef';
};

export const isAdmin = () => {
  return getUserRole() === 'Admin';
};

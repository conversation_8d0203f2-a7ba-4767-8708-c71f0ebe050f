const express = require("express");
const router = express.Router();

const {createRecipe,getAllRecipe,getRecipe,updateRecipe,deleteRecipe} = require("../controllers/recipeController");
const {isAuth,isChef} = require("../middlewares/auth");

router.post("/create",isAuth,isChef,createRecipe);
router.get("/all",isAuth,getAllRecipe);
router.get("/:id",isAuth,getRecipe);
router.put("/update/:id",isAuth,isChef,updateRecipe);
router.delete("/delete/:id",isAuth,isChef,deleteRecipe);

module.exports = router;
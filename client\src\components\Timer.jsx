import { useState, useEffect, useRef } from 'react';
import { Card, Form, Button, Alert } from 'react-bootstrap';
import { toast } from 'react-toastify';

const Timer = () => {
  const [minutes, setMinutes] = useState('');
  const [timeLeft, setTimeLeft] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const intervalRef = useRef(null);
  const audioRef = useRef(null);

  // Create audio context for beep sound
  useEffect(() => {
    // Create a simple beep sound using Web Audio API
    const createBeepSound = () => {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.value = 800; // Frequency in Hz
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 1);
    };

    audioRef.current = createBeepSound;
  }, []);

  useEffect(() => {
    if (isActive && !isPaused && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(time => {
          if (time <= 1) {
            // Timer finished
            setIsActive(false);
            setIsPaused(false);
            playBeepSound();
            toast.success('Timer finished! 🔔');
            return 0;
          }
          return time - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isActive, isPaused, timeLeft]);

  const playBeepSound = () => {
    try {
      if (audioRef.current) {
        audioRef.current();
      }
    } catch (error) {
      console.error('Error playing beep sound:', error);
    }
  };

  const startTimer = () => {
    const totalSeconds = parseInt(minutes) * 60;
    if (totalSeconds > 0) {
      setTimeLeft(totalSeconds);
      setIsActive(true);
      setIsPaused(false);
      toast.info(`Timer started for ${minutes} minutes`);
    } else {
      toast.error('Please enter a valid number of minutes');
    }
  };

  const pauseTimer = () => {
    setIsPaused(!isPaused);
    toast.info(isPaused ? 'Timer resumed' : 'Timer paused');
  };

  const resetTimer = () => {
    setIsActive(false);
    setIsPaused(false);
    setTimeLeft(0);
    setMinutes('');
    toast.info('Timer reset');
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimerColor = () => {
    if (!isActive) return 'secondary';
    if (timeLeft <= 60) return 'danger'; // Last minute
    if (timeLeft <= 300) return 'warning'; // Last 5 minutes
    return 'success';
  };

  return (
    <Card className="shadow-sm">
      <Card.Header className="bg-primary text-white">
        <h5 className="mb-0">
          <i className="fas fa-clock me-2"></i>
          Cooking Timer
        </h5>
      </Card.Header>
      <Card.Body>
        {!isActive ? (
          // Timer Setup
          <div>
            <Form.Group className="mb-3">
              <Form.Label>Set Timer (minutes)</Form.Label>
              <Form.Control
                type="number"
                min="1"
                max="180"
                value={minutes}
                onChange={(e) => setMinutes(e.target.value)}
                placeholder="Enter minutes"
              />
            </Form.Group>
            <Button
              variant="primary"
              onClick={startTimer}
              disabled={!minutes || minutes <= 0}
              className="w-100"
            >
              <i className="fas fa-play me-2"></i>
              Start Timer
            </Button>
          </div>
        ) : (
          // Active Timer
          <div className="text-center">
            <div className={`display-4 text-${getTimerColor()} mb-3`}>
              {formatTime(timeLeft)}
            </div>
            
            {timeLeft <= 60 && timeLeft > 0 && (
              <Alert variant="warning" className="mb-3">
                <i className="fas fa-exclamation-triangle me-2"></i>
                Less than 1 minute remaining!
              </Alert>
            )}
            
            <div className="d-flex gap-2 justify-content-center">
              <Button
                variant={isPaused ? "success" : "warning"}
                onClick={pauseTimer}
                size="sm"
              >
                <i className={`fas fa-${isPaused ? 'play' : 'pause'} me-1`}></i>
                {isPaused ? 'Resume' : 'Pause'}
              </Button>
              
              <Button
                variant="danger"
                onClick={resetTimer}
                size="sm"
              >
                <i className="fas fa-stop me-1"></i>
                Reset
              </Button>
            </div>
            
            {isPaused && (
              <div className="mt-2">
                <small className="text-muted">Timer is paused</small>
              </div>
            )}
          </div>
        )}
        
        <hr />
        
        {/* Quick Timer Buttons */}
        <div>
          <small className="text-muted d-block mb-2">Quick Timers:</small>
          <div className="d-flex gap-1 flex-wrap">
            {[5, 10, 15, 30].map((min) => (
              <Button
                key={min}
                variant="outline-secondary"
                size="sm"
                onClick={() => {
                  setMinutes(min.toString());
                  const totalSeconds = min * 60;
                  setTimeLeft(totalSeconds);
                  setIsActive(true);
                  setIsPaused(false);
                  toast.info(`Timer started for ${min} minutes`);
                }}
                disabled={isActive}
              >
                {min}m
              </Button>
            ))}
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};

export default Timer;

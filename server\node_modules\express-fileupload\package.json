{"name": "express-fileupload", "version": "1.5.2", "author": "<PERSON> <<EMAIL>>", "description": "Simple express file upload middleware that wraps around Busboy", "main": "./lib/index", "scripts": {"pretest": "node ./test/pretests.js", "posttest": "node ./test/posttests.js", "test": "nyc  --reporter=html --reporter=text mocha -- -R spec", "lint": "eslint ./", "lint:fix": "eslint --fix ./", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "dependencies": {"busboy": "^1.6.0"}, "engines": {"node": ">=12.0.0"}, "keywords": ["express", "file-upload", "upload", "forms", "multipart", "files", "busboy", "middleware"], "license": "MIT", "repository": "richardgirges/express-fileupload", "devDependencies": {"coveralls": "^3.1.1", "eslint": "^8.57.0", "express": "^4.21.2", "md5": "^2.3.0", "mocha": "^10.8.2", "nyc": "^15.1.0", "rimraf": "^3.0.2", "rnd-file": "^0.0.1", "supertest": "^6.3.4"}}
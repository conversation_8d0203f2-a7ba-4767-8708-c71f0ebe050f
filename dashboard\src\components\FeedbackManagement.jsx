import { useState, useEffect } from 'react';
import { Card, Table, Badge, Button, Row, Col, Form, InputGroup } from 'react-bootstrap';
import { toast } from 'react-toastify';
import axiosInstance from '../utils/axiosConfig';
import { FEEDBACK_ROUTES } from '../utils/apiRoutes';

const FeedbackManagement = () => {
  const [feedback, setFeedback] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRating, setFilterRating] = useState('');

  useEffect(() => {
    fetchFeedback();
  }, []);

  const fetchFeedback = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(FEEDBACK_ROUTES.GET_ALL);
      if (response.data.success) {
        setFeedback(response.data.feedback);
      }
    } catch (error) {
      console.error('Error fetching feedback:', error);
      toast.error('Failed to fetch feedback');
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating) => {
    return (
      <div className="d-flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <i
            key={star}
            className={`fas fa-star ${star <= rating ? 'text-warning' : 'text-muted'}`}
          />
        ))}
      </div>
    );
  };

  const getAverageRating = () => {
    if (feedback.length === 0) return 0;
    const total = feedback.reduce((sum, fb) => sum + fb.rating, 0);
    return (total / feedback.length).toFixed(1);
  };

  const getRatingDistribution = () => {
    const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    feedback.forEach(fb => {
      distribution[fb.rating]++;
    });
    return distribution;
  };

  const filteredFeedback = feedback.filter(fb => {
    const matchesSearch = 
      fb.recipe?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fb.user?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fb.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRating = filterRating === '' || fb.rating.toString() === filterRating;
    
    return matchesSearch && matchesRating;
  });

  if (loading) {
    return (
      <div className="text-center py-4">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-2">Loading feedback...</p>
      </div>
    );
  }

  const ratingDistribution = getRatingDistribution();

  return (
    <>
      {/* Feedback Statistics */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h3 className="text-primary">{feedback.length}</h3>
              <p className="mb-0">Total Reviews</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h3 className="text-warning">{getAverageRating()}</h3>
              <p className="mb-0">Average Rating</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={6}>
          <Card>
            <Card.Body>
              <h6>Rating Distribution</h6>
              {[5, 4, 3, 2, 1].map(rating => (
                <div key={rating} className="d-flex align-items-center mb-1">
                  <span className="me-2">{rating}</span>
                  <i className="fas fa-star text-warning me-2"></i>
                  <div className="progress flex-grow-1 me-2" style={{ height: '8px' }}>
                    <div
                      className="progress-bar bg-warning"
                      style={{
                        width: `${feedback.length > 0 ? (ratingDistribution[rating] / feedback.length) * 100 : 0}%`
                      }}
                    ></div>
                  </div>
                  <small className="text-muted">{ratingDistribution[rating]}</small>
                </div>
              ))}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card className="mb-4">
        <Card.Body>
          <Row>
            <Col md={8}>
              <Form.Group>
                <Form.Label>Search Feedback</Form.Label>
                <InputGroup>
                  <InputGroup.Text>
                    <i className="fas fa-search"></i>
                  </InputGroup.Text>
                  <Form.Control
                    type="text"
                    placeholder="Search by recipe name, user, or feedback content..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </InputGroup>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Filter by Rating</Form.Label>
                <Form.Select
                  value={filterRating}
                  onChange={(e) => setFilterRating(e.target.value)}
                >
                  <option value="">All Ratings</option>
                  <option value="5">5 Stars</option>
                  <option value="4">4 Stars</option>
                  <option value="3">3 Stars</option>
                  <option value="2">2 Stars</option>
                  <option value="1">1 Star</option>
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Feedback Table */}
      <Card>
        <Card.Header>
          <h5 className="mb-0">
            <i className="fas fa-comments me-2"></i>
            All Feedback ({filteredFeedback.length})
          </h5>
        </Card.Header>
        <Card.Body>
          {filteredFeedback.length > 0 ? (
            <Table responsive striped hover>
              <thead>
                <tr>
                  <th>Recipe</th>
                  <th>User</th>
                  <th>Rating</th>
                  <th>Feedback</th>
                  <th>Date</th>
                </tr>
              </thead>
              <tbody>
                {filteredFeedback.map((fb) => (
                  <tr key={fb._id}>
                    <td>
                      <div className="d-flex align-items-center">
                        {fb.recipe?.image && (
                          <img
                            src={fb.recipe.image}
                            alt={fb.recipe.name}
                            style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                            className="rounded me-2"
                          />
                        )}
                        <div>
                          <strong>{fb.recipe?.name || 'Unknown Recipe'}</strong>
                        </div>
                      </div>
                    </td>
                    <td>
                      <Badge bg="secondary">
                        {fb.user?.username || 'Anonymous'}
                      </Badge>
                    </td>
                    <td>
                      <div className="d-flex align-items-center">
                        {renderStars(fb.rating)}
                        <span className="ms-2">{fb.rating}/5</span>
                      </div>
                    </td>
                    <td>
                      <div style={{ maxWidth: '300px' }}>
                        {fb.description.length > 100
                          ? `${fb.description.substring(0, 100)}...`
                          : fb.description}
                      </div>
                    </td>
                    <td>
                      {new Date(fb.createdAt).toLocaleDateString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          ) : (
            <div className="text-center py-4">
              <i className="fas fa-comments fa-3x text-muted mb-3"></i>
              <h5>No feedback found</h5>
              <p className="text-muted">
                {searchTerm || filterRating
                  ? 'No feedback matches your search criteria.'
                  : 'No feedback has been submitted yet.'}
              </p>
            </div>
          )}
        </Card.Body>
      </Card>
    </>
  );
};

export default FeedbackManagement;

import { useState, useEffect } from 'react';
import { Card, Form, Button, ListGroup, Badge, Alert } from 'react-bootstrap';
import { toast } from 'react-toastify';
import axiosInstance from '../utils/axiosConfig';
import { FEEDBACK_ROUTES } from '../utils/apiRoutes';
import { getUserData } from '../utils/auth';

const FeedbackSection = ({ recipeId }) => {
  const [feedback, setFeedback] = useState([]);
  const [newFeedback, setNewFeedback] = useState({
    rating: 5,
    description: ''
  });
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const userData = getUserData();

  useEffect(() => {
    fetchFeedback();
  }, [recipeId]);

  const fetchFeedback = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(FEEDBACK_ROUTES.GET_ALL);
      if (response.data.success) {
        // Filter feedback for this specific recipe
        const recipeFeedback = response.data.feedback.filter(
          fb => fb.recipe._id === recipeId
        );
        setFeedback(recipeFeedback);
      }
    } catch (error) {
      console.error('Error fetching feedback:', error);
      toast.error('Failed to load feedback');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitFeedback = async (e) => {
    e.preventDefault();
    
    if (!newFeedback.description.trim()) {
      toast.error('Please write your feedback');
      return;
    }

    try {
      setSubmitting(true);
      const response = await axiosInstance.post(
        FEEDBACK_ROUTES.CREATE(recipeId),
        newFeedback
      );
      
      if (response.data.success) {
        toast.success('Feedback submitted successfully!');
        setNewFeedback({ rating: 5, description: '' });
        fetchFeedback(); // Refresh feedback list
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      const errorMessage = error.response?.data?.message || 'Failed to submit feedback';
      toast.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const renderStars = (rating, interactive = false, onRatingChange = null) => {
    return (
      <div className="d-flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <i
            key={star}
            className={`fas fa-star ${star <= rating ? 'text-warning' : 'text-muted'} ${
              interactive ? 'cursor-pointer' : ''
            }`}
            style={{ cursor: interactive ? 'pointer' : 'default' }}
            onClick={() => interactive && onRatingChange && onRatingChange(star)}
          />
        ))}
      </div>
    );
  };

  const getAverageRating = () => {
    if (feedback.length === 0) return 0;
    const total = feedback.reduce((sum, fb) => sum + fb.rating, 0);
    return (total / feedback.length).toFixed(1);
  };

  return (
    <Card className="mb-4">
      <Card.Header>
        <div className="d-flex justify-content-between align-items-center">
          <h4><i className="fas fa-comments me-2"></i>Feedback & Reviews</h4>
          {feedback.length > 0 && (
            <div className="d-flex align-items-center">
              {renderStars(Math.round(getAverageRating()))}
              <span className="ms-2 text-muted">
                {getAverageRating()} ({feedback.length} review{feedback.length !== 1 ? 's' : ''})
              </span>
            </div>
          )}
        </div>
      </Card.Header>
      <Card.Body>
        {/* Submit New Feedback */}
        <div className="mb-4">
          <h6>Share Your Experience</h6>
          <Form onSubmit={handleSubmitFeedback}>
            <Form.Group className="mb-3">
              <Form.Label>Rating</Form.Label>
              <div>
                {renderStars(newFeedback.rating, true, (rating) =>
                  setNewFeedback({ ...newFeedback, rating })
                )}
              </div>
            </Form.Group>
            
            <Form.Group className="mb-3">
              <Form.Label>Your Review</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={newFeedback.description}
                onChange={(e) =>
                  setNewFeedback({ ...newFeedback, description: e.target.value })
                }
                placeholder="Share your thoughts about this recipe..."
                required
              />
            </Form.Group>
            
            <Button
              type="submit"
              variant="primary"
              disabled={submitting}
            >
              {submitting ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2"></span>
                  Submitting...
                </>
              ) : (
                <>
                  <i className="fas fa-paper-plane me-2"></i>
                  Submit Review
                </>
              )}
            </Button>
          </Form>
        </div>

        <hr />

        {/* Display Existing Feedback */}
        <div>
          <h6>Reviews ({feedback.length})</h6>
          {loading ? (
            <div className="text-center py-3">
              <div className="spinner-border spinner-border-sm text-primary"></div>
              <p className="mt-2 mb-0">Loading reviews...</p>
            </div>
          ) : feedback.length > 0 ? (
            <ListGroup variant="flush">
              {feedback.map((fb, index) => (
                <ListGroup.Item key={index} className="px-0">
                  <div className="d-flex justify-content-between align-items-start mb-2">
                    <div>
                      <strong>{fb.user?.username || 'Anonymous'}</strong>
                      <div className="d-flex align-items-center mt-1">
                        {renderStars(fb.rating)}
                        <Badge bg="secondary" className="ms-2">
                          {fb.rating}/5
                        </Badge>
                      </div>
                    </div>
                    <small className="text-muted">
                      {new Date(fb.createdAt).toLocaleDateString()}
                    </small>
                  </div>
                  <p className="mb-0">{fb.description}</p>
                </ListGroup.Item>
              ))}
            </ListGroup>
          ) : (
            <Alert variant="info" className="mb-0">
              <i className="fas fa-info-circle me-2"></i>
              No reviews yet. Be the first to share your experience!
            </Alert>
          )}
        </div>
      </Card.Body>
    </Card>
  );
};

export default FeedbackSection;

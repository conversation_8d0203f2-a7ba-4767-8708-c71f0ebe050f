import { useState, useEffect } from 'react';
import { Modal, Form, Button, Row, Col, Alert } from 'react-bootstrap';
import { toast } from 'react-toastify';
import axiosInstance from '../utils/axiosConfig';
import { RECIPE_ROUTES } from '../utils/apiRoutes';

const RecipeModal = ({ show, onHide, recipe }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    ingredients: [''],
    steps: [''],
    image: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (recipe) {
      // Editing existing recipe
      setFormData({
        name: recipe.name || '',
        description: recipe.description || '',
        ingredients: recipe.ingredients || [''],
        steps: recipe.steps || [''],
        image: recipe.image || ''
      });
    } else {
      // Adding new recipe
      setFormData({
        name: '',
        description: '',
        ingredients: [''],
        steps: [''],
        image: ''
      });
    }
    setError('');
  }, [recipe, show]);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    if (error) setError('');
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setFormData({
          ...formData,
          image: reader.result
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleArrayChange = (index, value, field) => {
    const newArray = [...formData[field]];
    newArray[index] = value;
    setFormData({
      ...formData,
      [field]: newArray
    });
  };

  const addArrayItem = (field) => {
    setFormData({
      ...formData,
      [field]: [...formData[field], '']
    });
  };

  const removeArrayItem = (index, field) => {
    if (formData[field].length > 1) {
      const newArray = formData[field].filter((_, i) => i !== index);
      setFormData({
        ...formData,
        [field]: newArray
      });
    }
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError('Recipe name is required');
      return false;
    }
    if (!formData.description.trim()) {
      setError('Recipe description is required');
      return false;
    }
    if (!formData.image && !recipe) {
      setError('Recipe image is required');
      return false;
    }
    if (formData.ingredients.some(ing => !ing.trim())) {
      setError('All ingredients must be filled');
      return false;
    }
    if (formData.steps.some(step => !step.trim())) {
      setError('All steps must be filled');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      setError('');

      // Filter out empty ingredients and steps
      const submitData = {
        ...formData,
        ingredients: formData.ingredients.filter(ing => ing.trim()),
        steps: formData.steps.filter(step => step.trim())
      };

      let response;
      if (recipe) {
        // Update existing recipe
        response = await axiosInstance.put(RECIPE_ROUTES.UPDATE(recipe._id), submitData);
      } else {
        // Create new recipe
        response = await axiosInstance.post(RECIPE_ROUTES.CREATE, submitData);
      }

      if (response.data.success) {
        toast.success(recipe ? 'Recipe updated successfully!' : 'Recipe created successfully!');
        onHide();
      }
    } catch (error) {
      console.error('Error saving recipe:', error);
      const errorMessage = error.response?.data?.message || 'Failed to save recipe';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header closeButton>
        <Modal.Title>
          {recipe ? 'Edit Recipe' : 'Add New Recipe'}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {error && (
          <Alert variant="danger" className="mb-3">
            {error}
          </Alert>
        )}

        <Form onSubmit={handleSubmit}>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Recipe Name *</Form.Label>
                <Form.Control
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter recipe name"
                  required
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Recipe Image {!recipe && '*'}</Form.Label>
                <Form.Control
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  required={!recipe}
                />
                {recipe && recipe.image && (
                  <div className="mt-2">
                    <img
                      src={recipe.image}
                      alt="Current recipe"
                      style={{ width: '100px', height: '100px', objectFit: 'cover' }}
                      className="rounded"
                    />
                  </div>
                )}
              </Form.Group>
            </Col>
          </Row>

          <Form.Group className="mb-3">
            <Form.Label>Description *</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Enter recipe description"
              required
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Ingredients *</Form.Label>
            {formData.ingredients.map((ingredient, index) => (
              <div key={index} className="d-flex mb-2">
                <Form.Control
                  type="text"
                  value={ingredient}
                  onChange={(e) => handleArrayChange(index, e.target.value, 'ingredients')}
                  placeholder={`Ingredient ${index + 1}`}
                  required
                />
                <Button
                  variant="outline-danger"
                  className="ms-2"
                  onClick={() => removeArrayItem(index, 'ingredients')}
                  disabled={formData.ingredients.length === 1}
                >
                  <i className="fas fa-trash"></i>
                </Button>
              </div>
            ))}
            <Button
              variant="outline-primary"
              size="sm"
              onClick={() => addArrayItem('ingredients')}
            >
              <i className="fas fa-plus me-2"></i>
              Add Ingredient
            </Button>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Cooking Steps *</Form.Label>
            {formData.steps.map((step, index) => (
              <div key={index} className="d-flex mb-2">
                <Form.Control
                  as="textarea"
                  rows={2}
                  value={step}
                  onChange={(e) => handleArrayChange(index, e.target.value, 'steps')}
                  placeholder={`Step ${index + 1}`}
                  required
                />
                <Button
                  variant="outline-danger"
                  className="ms-2"
                  onClick={() => removeArrayItem(index, 'steps')}
                  disabled={formData.steps.length === 1}
                >
                  <i className="fas fa-trash"></i>
                </Button>
              </div>
            ))}
            <Button
              variant="outline-primary"
              size="sm"
              onClick={() => addArrayItem('steps')}
            >
              <i className="fas fa-plus me-2"></i>
              Add Step
            </Button>
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2"></span>
              {recipe ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            recipe ? 'Update Recipe' : 'Create Recipe'
          )}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default RecipeModal;

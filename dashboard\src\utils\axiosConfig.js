import axios from 'axios';

const axiosInstance = axios.create({
  baseURL: "http://localhost:4000",
  withCredentials: true,
});

// Request interceptor to add auth token if available
axiosInstance.interceptors.request.use(
  (config) => {
    // Token is handled via cookies, so no need to manually add it
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid, redirect to login
      window.location.href = '/dashboard/login';
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;

{"author": "Cloudinary <<EMAIL>>", "name": "cloudinary", "description": "Cloudinary NPM for node.js integration", "version": "2.7.0", "homepage": "https://cloudinary.com", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cloudinary/cloudinary_npm.git"}, "main": "cloudinary.js", "dependencies": {"lodash": "^4.17.21", "q": "^1.5.1"}, "devDependencies": {"@types/expect.js": "^0.3.29", "@types/mocha": "^7.0.2", "@types/node": "^13.5.0", "date-fns": "^2.16.1", "dotenv": "4.x", "dtslint": "^0.9.1", "eslint": "^6.8.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.20.2", "expect.js": "0.3.x", "glob": "^7.1.6", "jsdoc": "^4.0.4", "jsdom": "^9.12.0", "jsdom-global": "2.1.1", "mocha": "^6.2.3", "nyc": "^14.1.1", "rimraf": "^3.0.0", "sinon": "^6.1.4", "typescript": "^3.7.5", "webpack-cli": "^3.2.1"}, "files": ["lib/**/*", "cloudinary.js", "babel.config.js", "package.json", "types/index.d.ts"], "types": "types", "scripts": {"test": "tools/scripts/test.sh", "test:unit": "tools/scripts/test.es6.unit.sh", "test-with-temp-cloud": "tools/scripts/tests-with-temp-cloud.sh", "dtslint": "tools/scripts/ditslint.sh", "lint": "tools/scripts/lint.sh", "coverage": "tools/scripts/test.es6.sh --coverage", "test-es6": "tools/scripts/test.es6.sh", "docs": "tools/scripts/docs.sh"}, "engines": {"node": ">=9"}}
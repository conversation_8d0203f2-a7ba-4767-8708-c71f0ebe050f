<script type="text/javascript">
    RED.nodes.registerType("text-to-speech", {
        category: "google",
        color: "#FAFAFA",
        defaults: {
            name: { value: "" },
            voice: { value: "en-US", required: true },
        },
        inputs: 1,
        outputs: 1,
        icon: "feed.svg",
        label: function () {
            return this.name || "text-to-speech";
        },
    });
</script>

<script type="text/html" data-template-name="text-to-speech">
    <div class="form-row">
        <label for="node-input-name">Name</label>
        <input type="text" id="node-input-name" placeholder="Name" />
    </div>
    <div class="form-row">
        <label for="node-input-voice">Voice code</label>
        <input type="text" id="node-input-voice" placeholder="Voice code" />
    </div>
</script>

<script type="text/html" data-help-name="text-to-speech">
    <p>
        Uses Google Translate's text-to-speech service to synthesize any string
        into speech.
    </p>
    <h3>Outputs</h3>
    <dl class="message-properties">
        <dt>payload <span class="property-type">buffer</span></dt>
        <dd>a buffer containing raw mp3 audio data</dd>
    </dl>
    <h3>Properties</h3>
    <dl class="message-properties">
        <dt>voice <span class="property-type">string</span></dt>
        <dd>
            the voice code used to synthesize speech. check
            <a href="https://cloud.google.com/speech-to-text/docs/languages">
                here
            </a>
            for a list of language codes that might work.
        </dd>
    </dl>
    <h3>Details</h3>
    <p>
        This node takes a string of text as input, uses the Google Translate API
        to synthesize the text into speech, and returns a buffer of raw audio
        data. This output can be played, saved to a file, or sent to another
        node. The default voice used is English (US), but this can be changed in
        the node properties. You can check
        <a href="https://cloud.google.com/speech-to-text/docs/languages">
            Google's documentation
        </a>
        for a list of language codes.
    </p>
</script>
